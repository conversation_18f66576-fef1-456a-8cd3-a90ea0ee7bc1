"""
Easy training script - The ultimate cheat code for training bots!

This script makes it super simple to train different types of bots with just one command.
No need to mess with complex configurations - just pick a preset and go!

Examples:
    # Train a Nexto-style bot
    python -m rlgym_framework.easy_train --preset nexto

    # Train a ground specialist
    python -m rlgym_framework.easy_train --preset ground --name my_ground_bot

    # Train an all-around bot with custom timesteps
    python -m rlgym_framework.easy_train --preset all_around --timesteps 50000000

    # List all available presets
    python -m rlgym_framework.easy_train --list-presets
"""

import argparse
import sys
from typing import Optional

from .presets import get_preset_config, list_available_presets, get_preset_descriptions
from .environments import RLGymEnvironment
from .agents import PPOAgent
from .training import Trainer
from .core import get_device_manager
from .utils import setup_logging


def main():
    """Main entry point for easy training."""
    parser = argparse.ArgumentParser(
        description="Easy bot training with presets - The ultimate cheat code!",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--preset", "-p",
        type=str,
        help="Bot preset to use (e.g., nexto, ground, aerial, all_around)"
    )
    
    parser.add_argument(
        "--name", "-n",
        type=str,
        help="Custom name for your bot (optional)"
    )
    
    parser.add_argument(
        "--timesteps", "-t",
        type=int,
        help="Total training timesteps (overrides preset default)"
    )
    
    parser.add_argument(
        "--device", "-d",
        type=str,
        help="Device to use (cuda, directml, rocm, cpu, or auto)"
    )
    
    parser.add_argument(
        "--save-dir", "-s",
        type=str,
        default="./models",
        help="Directory to save models (default: ./models)"
    )
    
    parser.add_argument(
        "--log-dir", "-l",
        type=str,
        default="./logs",
        help="Directory for logs (default: ./logs)"
    )
    
    parser.add_argument(
        "--list-presets",
        action="store_true",
        help="List all available presets and exit"
    )
    
    parser.add_argument(
        "--describe-presets",
        action="store_true",
        help="Show detailed descriptions of all presets and exit"
    )
    
    parser.add_argument(
        "--wandb",
        action="store_true",
        help="Enable Weights & Biases logging"
    )
    
    parser.add_argument(
        "--no-tensorboard",
        action="store_true",
        help="Disable TensorBoard logging"
    )
    
    args = parser.parse_args()
    
    # Handle preset listing
    if args.list_presets:
        print("Available bot presets:")
        for preset in list_available_presets():
            print(f"  - {preset}")
        return
    
    if args.describe_presets:
        print("Available bot presets with descriptions:")
        descriptions = get_preset_descriptions()
        for preset, description in descriptions.items():
            print(f"\n{preset.upper()}:")
            print(f"  {description}")
        return
    
    # Validate required arguments
    if not args.preset:
        print("Error: --preset is required. Use --list-presets to see available options.")
        sys.exit(1)
    
    try:
        # Load preset configuration
        print(f"Loading preset: {args.preset}")
        config = get_preset_config(args.preset)
        
        # Apply command line overrides
        if args.name:
            config.experiment["name"] = args.name
        
        if args.timesteps:
            config.training.total_timesteps = args.timesteps
        
        if args.device:
            config.training.device = args.device
        
        config.training.save_dir = args.save_dir
        config.training.log_dir = args.log_dir
        config.training.use_wandb = args.wandb
        config.training.use_tensorboard = not args.no_tensorboard
        
        # Set experiment name for logging
        if not config.training.experiment_name:
            config.training.experiment_name = config.get_experiment_name()
        
        print(f"Training bot: {config.experiment.get('name', 'Unknown')}")
        print(f"Description: {config.experiment.get('description', 'No description')}")
        print(f"Total timesteps: {config.training.total_timesteps:,}")
        
        # Initialize device manager
        device_manager = get_device_manager(config.training.device)
        print(f"Using device: {device_manager.current_device}")
        
        # Set up logging
        setup_logging(
            log_dir=config.training.log_dir,
            level="INFO"
        )
        
        # Create environment
        print("Creating environment...")
        env = RLGymEnvironment(
            env_config=config.environment,
            device_manager=device_manager
        )
        
        # Create agent
        print("Creating agent...")
        agent = PPOAgent(
            observation_size=env.observation_space.shape[-1],
            action_size=env.action_space.shape[-1],
            device_manager=device_manager,
            **config.agent,
            **{k: v for k, v in config.training.to_dict().items() 
               if k in ['learning_rate', 'hidden_sizes', 'clip_ratio', 'entropy_coef', 
                       'value_loss_coef', 'max_grad_norm', 'ppo_epochs', 'gamma', 'gae_lambda']}
        )
        
        # Create trainer
        print("Creating trainer...")
        trainer = Trainer(
            agent=agent,
            env=env,
            config=config.training,
            device_manager=device_manager
        )
        
        # Start training
        print("\n" + "="*50)
        print("STARTING TRAINING - LET'S MAKE A BOT!")
        print("="*50)
        
        results = trainer.train()
        
        print("\n" + "="*50)
        print("TRAINING COMPLETED!")
        print("="*50)
        print(f"Total timesteps: {results['total_timesteps']:,}")
        print(f"Episodes completed: {results['episode_count']:,}")
        print(f"Training time: {results['total_time']:.2f} seconds")
        print(f"Final evaluation reward: {results['final_eval']['mean_reward']:.2f}")
        print(f"Model saved to: {config.training.save_dir}")
        
    except KeyboardInterrupt:
        print("\nTraining interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"Error during training: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
