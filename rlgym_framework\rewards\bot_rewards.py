"""
Comprehensive reward functions for different bot styles.
Each style is carefully crafted to produce bots with specific characteristics.
"""

import numpy as np
from typing import Any, Dict
from .reward_manager import BaseRewardFunction


class NextoRewards(BaseRewardFunction):
    """
    Nexto-style rewards: Mechanical, aggressive, fast-paced gameplay.
    Focus on air dribbles, flip resets, and high-speed plays.
    """
    
    def __init__(self):
        super().__init__()
        self.air_time = 0
        self.consecutive_touches = 0
        self.last_touch_time = 0
        
    def reset(self, initial_state: Any) -> None:
        self.air_time = 0
        self.consecutive_touches = 0
        self.last_touch_time = 0
        self.episode_data = {}
        
    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0
        
        # Basic goal/save rewards
        if state.last_touch == player.car_id:
            if hasattr(state, 'blue_score') and hasattr(state, 'orange_score'):
                # Goal scored
                if (player.team_num == 0 and state.blue_score > getattr(self, 'prev_blue_score', 0)) or \
                   (player.team_num == 1 and state.orange_score > getattr(self, 'prev_orange_score', 0)):
                    reward += 100.0
                    # Bonus for aerial goals
                    if player.car_data.position[2] > 300:
                        reward += 50.0
        
        # Mechanical play rewards
        ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
        
        # Air dribble detection and reward
        if player.car_data.position[2] > 200 and state.ball.position[2] > 200:
            self.air_time += 1
            if ball_distance < 200:
                reward += 5.0  # Air dribble reward
                self.consecutive_touches += 1
                if self.consecutive_touches > 3:
                    reward += 10.0  # Extended air dribble bonus
        else:
            self.air_time = 0
            self.consecutive_touches = 0
            
        # Speed rewards (Nexto is known for speed)
        speed = np.linalg.norm(player.car_data.linear_velocity)
        if speed > 1800:
            reward += 2.0
        if speed > 2200:
            reward += 3.0
            
        # Ball touch rewards with mechanical bonus
        if ball_distance < 150:
            reward += 3.0
            # Bonus for touches while supersonic
            if speed > 2200:
                reward += 5.0
                
        # Flip reset detection (simplified)
        if player.car_data.position[2] > 100 and ball_distance < 120:
            if abs(player.car_data.angular_velocity[0]) > 3 or abs(player.car_data.angular_velocity[1]) > 3:
                reward += 15.0  # Flip reset bonus
                
        # Aggressive positioning
        enemy_goal = np.array([0, 5120 if player.team_num == 0 else -5120, 0])
        goal_distance = np.linalg.norm(player.car_data.position - enemy_goal)
        if goal_distance < 3000:
            reward += 1.0  # Aggressive positioning
            
        # Boost management (Nexto-style: aggressive boost usage)
        if player.boost_amount > 50:
            reward += 0.5
        if player.boost_amount < 20 and speed > 1500:
            reward -= 1.0  # Penalty for poor boost management
            
        return reward


class OptiRewards(BaseRewardFunction):
    """
    Opti-style rewards: Positioning master, game sense, efficient plays.
    Focus on smart rotations, boost efficiency, and consistent performance.
    """
    
    def __init__(self):
        super().__init__()
        self.boost_pickups = 0
        self.good_positions = 0
        
    def reset(self, initial_state: Any) -> None:
        self.boost_pickups = 0
        self.good_positions = 0
        self.episode_data = {}
        
    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0
        
        # Goal and save rewards
        if state.last_touch == player.car_id:
            reward += 50.0  # Conservative goal reward
            
        # Positioning rewards (Opti's strength)
        ball_pos = state.ball.position
        player_pos = player.car_data.position
        
        # Defensive positioning
        own_goal = np.array([0, -5120 if player.team_num == 0 else 5120, 0])
        goal_to_ball = ball_pos - own_goal
        goal_to_player = player_pos - own_goal
        
        # Check if player is between ball and goal
        if np.dot(goal_to_ball, goal_to_player) > 0:
            distance_from_line = np.linalg.norm(np.cross(goal_to_ball, goal_to_player)) / np.linalg.norm(goal_to_ball)
            if distance_from_line < 1000:
                reward += 3.0  # Good defensive position
                
        # Boost efficiency (Opti's hallmark)
        if player.boost_amount > 70:
            reward += 2.0
        elif player.boost_amount < 30:
            # Penalty unless near boost pad
            boost_pads = self._get_boost_pad_positions()
            nearest_boost_dist = min([np.linalg.norm(player_pos - pad) for pad in boost_pads])
            if nearest_boost_dist > 500:
                reward -= 2.0
                
        # Smart boost pickup
        prev_boost = getattr(self, 'prev_boost', player.boost_amount)
        if player.boost_amount > prev_boost:
            self.boost_pickups += 1
            if player.boost_amount - prev_boost >= 100:  # Big boost
                reward += 3.0
            else:  # Small boost
                reward += 1.0
        self.prev_boost = player.boost_amount
        
        # Ball distance management
        ball_distance = np.linalg.norm(player_pos - ball_pos)
        if 500 < ball_distance < 2000:  # Good positioning distance
            reward += 1.0
            
        # Speed efficiency
        speed = np.linalg.norm(player.car_data.linear_velocity)
        if 1000 < speed < 1800:  # Efficient speed
            reward += 1.0
            
        return reward
    
    def _get_boost_pad_positions(self):
        """Get boost pad positions (simplified)."""
        return [
            np.array([3584, 0, 0]), np.array([-3584, 0, 0]),  # Corner boosts
            np.array([0, 4240, 0]), np.array([0, -4240, 0]),  # Goal boosts
        ]


class RippleRewards(BaseRewardFunction):
    """
    Ripple-style rewards: Mechanical genius, creative plays, unpredictable.
    Focus on advanced mechanics, ceiling shots, and innovative plays.
    """
    
    def __init__(self):
        super().__init__()
        self.ceiling_time = 0
        self.wall_time = 0
        self.creative_plays = 0
        
    def reset(self, initial_state: Any) -> None:
        self.ceiling_time = 0
        self.wall_time = 0
        self.creative_plays = 0
        self.episode_data = {}
        
    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0
        
        # Goal rewards with mechanical bonuses
        if state.last_touch == player.car_id:
            reward += 75.0
            # Massive bonus for ceiling shots
            if player.car_data.position[2] > 1000:
                reward += 100.0
            # Bonus for wall shots
            if abs(player.car_data.position[0]) > 3500 or abs(player.car_data.position[1]) > 4500:
                reward += 50.0
                
        # Ceiling play detection
        if player.car_data.position[2] > 1800:  # Near ceiling
            self.ceiling_time += 1
            reward += 3.0
            if self.ceiling_time > 30:  # Extended ceiling time
                reward += 5.0
        else:
            self.ceiling_time = 0
            
        # Wall play detection
        if abs(player.car_data.position[0]) > 3800 or abs(player.car_data.position[1]) > 4800:
            self.wall_time += 1
            reward += 2.0
        else:
            self.wall_time = 0
            
        # Advanced mechanical rewards
        ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
        
        # Air roll detection (simplified)
        angular_vel = np.linalg.norm(player.car_data.angular_velocity)
        if angular_vel > 4 and player.car_data.position[2] > 200:
            reward += 4.0  # Air roll bonus
            
        # Creative play detection
        if player.car_data.position[2] > 500 and ball_distance < 200:
            if angular_vel > 3:
                self.creative_plays += 1
                reward += 8.0  # Creative mechanical play
                
        # Boost management for mechanics
        if player.boost_amount > 40:
            reward += 1.0
        if player.boost_amount > 80:
            reward += 2.0  # Need boost for mechanics
            
        return reward


class SeerRewards(BaseRewardFunction):
    """
    Seer-style rewards: Defensive wall, incredible saves, positioning.
    Focus on saves, clears, and defensive excellence.
    """
    
    def __init__(self):
        super().__init__()
        self.saves_made = 0
        self.defensive_touches = 0
        
    def reset(self, initial_state: Any) -> None:
        self.saves_made = 0
        self.defensive_touches = 0
        self.episode_data = {}
        
    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0
        
        # Massive save rewards
        ball_pos = state.ball.position
        ball_vel = state.ball.linear_velocity
        own_goal = np.array([0, -5120 if player.team_num == 0 else 5120, 0])
        
        # Save detection
        goal_distance = np.linalg.norm(ball_pos - own_goal)
        if goal_distance < 1500:  # Ball near goal
            ball_to_goal = own_goal - ball_pos
            ball_direction = ball_vel / (np.linalg.norm(ball_vel) + 1e-8)
            
            if np.dot(ball_to_goal, ball_direction) > 0:  # Ball heading to goal
                if state.last_touch == player.car_id:
                    reward += 150.0  # Massive save reward
                    self.saves_made += 1
                    
        # Defensive positioning
        player_pos = player.car_data.position
        goal_to_ball = ball_pos - own_goal
        goal_to_player = player_pos - own_goal
        
        # Reward for being between ball and goal
        if np.dot(goal_to_ball, goal_to_player) > 0:
            reward += 5.0
            
        # Clear rewards
        if state.last_touch == player.car_id:
            if goal_distance < 2000:  # Defensive clear
                reward += 25.0
                self.defensive_touches += 1
                
        # Boost management for saves
        if goal_distance < 2000 and player.boost_amount > 50:
            reward += 3.0  # Good boost for defense
            
        # Positioning near goal when needed
        player_goal_distance = np.linalg.norm(player_pos - own_goal)
        if goal_distance < 3000 and player_goal_distance < 2000:
            reward += 2.0  # Good defensive position
            
        return reward


class GroundSpecialistRewards(BaseRewardFunction):
    """
    Ground specialist: Master of dribbling, flicks, and ground plays.
    """

    def __init__(self):
        super().__init__()
        self.dribble_time = 0
        self.flicks_performed = 0

    def reset(self, initial_state: Any) -> None:
        self.dribble_time = 0
        self.flicks_performed = 0
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Ground goal rewards
        if state.last_touch == player.car_id:
            if player.car_data.position[2] < 200:
                reward += 100.0  # Ground goal
                if self.dribble_time > 60:
                    reward += 50.0  # Dribble goal bonus

        ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)

        # Dribbling rewards
        if (player.car_data.position[2] < 100 and state.ball.position[2] < 200 and
            ball_distance < 150):
            self.dribble_time += 1
            reward += 5.0

            # Extended dribble bonus
            if self.dribble_time > 30:
                reward += 3.0

        # Flick detection
        if (self.dribble_time > 10 and state.ball.linear_velocity[2] > 500 and
            ball_distance < 200):
            self.flicks_performed += 1
            reward += 30.0

        # 50/50 rewards
        speed = np.linalg.norm(player.car_data.linear_velocity)
        if ball_distance < 200 and speed > 1500:
            reward += 8.0  # Aggressive challenge

        # Boost management for ground play
        if 20 < player.boost_amount < 60:
            reward += 1.0  # Conservative boost use

        return reward


class AerialSpecialistRewards(BaseRewardFunction):
    """
    Aerial specialist: Dominates the air with control and precision.
    """

    def __init__(self):
        super().__init__()
        self.air_time = 0
        self.aerial_goals = 0

    def reset(self, initial_state: Any) -> None:
        self.air_time = 0
        self.aerial_goals = 0
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Aerial goal rewards
        if state.last_touch == player.car_id:
            if player.car_data.position[2] > 300:
                reward += 120.0
                self.aerial_goals += 1

                # Height bonus
                height_bonus = min(50.0, player.car_data.position[2] / 20)
                reward += height_bonus

        # Air time rewards
        if player.car_data.position[2] > 200:
            self.air_time += 1
            reward += 2.0

            ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
            if ball_distance < 300:
                reward += 5.0  # Aerial control

        # Aerial save rewards
        ball_pos = state.ball.position
        own_goal = np.array([0, -5120 if player.team_num == 0 else 5120, 0])
        if (np.linalg.norm(ball_pos - own_goal) < 2000 and
            player.car_data.position[2] > 200 and state.last_touch == player.car_id):
            reward += 80.0  # Aerial save

        # Boost management for aerials
        if player.boost_amount > 50:
            reward += 2.0  # Need boost for aerials

        return reward


class AllAroundRewards(BaseRewardFunction):
    """
    All-around balanced bot: Good at everything, master of adaptation.
    """

    def __init__(self):
        super().__init__()
        self.skills_used = set()

    def reset(self, initial_state: Any) -> None:
        self.skills_used = set()
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Balanced goal rewards
        if state.last_touch == player.car_id:
            reward += 80.0

            # Track different types of goals
            if player.car_data.position[2] < 150:
                self.skills_used.add('ground')
                reward += 20.0
            elif player.car_data.position[2] > 300:
                self.skills_used.add('aerial')
                reward += 25.0

        # Versatility bonus
        if len(self.skills_used) > 2:
            reward += 5.0

        # Ball control
        ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
        if ball_distance < 200:
            reward += 3.0

        # Positioning
        ball_pos = state.ball.position
        player_pos = player.car_data.position
        if 300 < np.linalg.norm(player_pos - ball_pos) < 1500:
            reward += 2.0  # Good positioning

        # Boost efficiency
        if 30 < player.boost_amount < 70:
            reward += 1.5

        return reward


class PositioningRewards(BaseRewardFunction):
    """
    Positioning specialist: Game sense and smart rotations.
    """

    def __init__(self):
        super().__init__()
        self.good_positions = 0

    def reset(self, initial_state: Any) -> None:
        self.good_positions = 0
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Smart goal rewards
        if state.last_touch == player.car_id:
            reward += 60.0  # Lower base reward, focus on positioning

        # Positioning analysis
        ball_pos = state.ball.position
        player_pos = player.car_data.position
        own_goal = np.array([0, -5120 if player.team_num == 0 else 5120, 0])
        enemy_goal = np.array([0, 5120 if player.team_num == 0 else -5120, 0])

        # Defensive positioning
        goal_to_ball = ball_pos - own_goal
        goal_to_player = player_pos - own_goal

        if np.dot(goal_to_ball, goal_to_player) > 0:
            reward += 4.0  # Between ball and goal
            self.good_positions += 1

        # Offensive positioning
        ball_to_goal = enemy_goal - ball_pos
        ball_to_player = player_pos - ball_pos

        if np.dot(ball_to_goal, ball_to_player) > 0:
            reward += 3.0  # Good attacking position

        # Boost efficiency
        if player.boost_amount > 60:
            reward += 2.0
        elif player.boost_amount < 20:
            reward -= 1.0

        return reward


class SpeedDemonRewards(BaseRewardFunction):
    """
    Speed demon: Fast-paced, high-tempo gameplay.
    """

    def __init__(self):
        super().__init__()
        self.supersonic_time = 0

    def reset(self, initial_state: Any) -> None:
        self.supersonic_time = 0
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Speed-based goal rewards
        speed = np.linalg.norm(player.car_data.linear_velocity)
        if state.last_touch == player.car_id:
            reward += 70.0
            if speed > 2000:
                reward += 40.0  # High-speed goal

        # Speed rewards
        if speed > 2200:  # Supersonic
            self.supersonic_time += 1
            reward += 3.0

        if speed > 1800:
            reward += 1.5

        # Fast challenges
        ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
        if ball_distance < 200 and speed > 1800:
            reward += 8.0  # Fast challenge

        # Boost usage for speed
        if player.boost_amount > 40 and speed > 1500:
            reward += 2.0

        return reward


class WallPlayRewards(BaseRewardFunction):
    """
    Wall play specialist: Master of wall control and wall shots.
    """

    def __init__(self):
        super().__init__()
        self.wall_time = 0

    def reset(self, initial_state: Any) -> None:
        self.wall_time = 0
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Wall shot rewards
        if state.last_touch == player.car_id:
            if (abs(player.car_data.position[0]) > 3000 or
                abs(player.car_data.position[1]) > 4000):
                reward += 100.0  # Wall shot

        # Wall time rewards
        if (abs(player.car_data.position[0]) > 3500 or
            abs(player.car_data.position[1]) > 4500):
            self.wall_time += 1
            reward += 3.0

            ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
            if ball_distance < 200:
                reward += 6.0  # Wall control

        # Wall-to-air transitions
        if (self.wall_time > 10 and player.car_data.position[2] > 300 and
            abs(player.car_data.position[0]) < 3000):
            reward += 15.0  # Wall-to-air transition

        return reward


class DemoHunterRewards(BaseRewardFunction):
    """
    Demo hunter: Aggressive, disruptive gameplay.
    """

    def __init__(self):
        super().__init__()
        self.demos = 0

    def reset(self, initial_state: Any) -> None:
        self.demos = 0
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Goal rewards
        if state.last_touch == player.car_id:
            reward += 60.0

        # Demo rewards (simplified detection)
        speed = np.linalg.norm(player.car_data.linear_velocity)
        if speed > 2000:
            reward += 2.0  # High speed for demos

        # Aggressive positioning
        enemy_goal = np.array([0, 5120 if player.team_num == 0 else -5120, 0])
        goal_distance = np.linalg.norm(player.car_data.position - enemy_goal)
        if goal_distance < 2000:
            reward += 3.0  # Aggressive positioning

        # Boost for aggression
        if player.boost_amount > 50:
            reward += 1.5

        return reward


class DefensiveRewards(BaseRewardFunction):
    """
    Defensive specialist: The last line of defense.
    """

    def __init__(self):
        super().__init__()
        self.saves = 0

    def reset(self, initial_state: Any) -> None:
        self.saves = 0
        self.episode_data = {}

    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0

        # Save rewards
        ball_pos = state.ball.position
        own_goal = np.array([0, -5120 if player.team_num == 0 else 5120, 0])
        goal_distance = np.linalg.norm(ball_pos - own_goal)

        if goal_distance < 1500 and state.last_touch == player.car_id:
            reward += 120.0  # Save
            self.saves += 1

        # Defensive positioning
        player_pos = player.car_data.position
        goal_to_ball = ball_pos - own_goal
        goal_to_player = player_pos - own_goal

        if np.dot(goal_to_ball, goal_to_player) > 0:
            reward += 5.0  # Good defensive position

        # Clear rewards
        if state.last_touch == player.car_id and goal_distance < 3000:
            reward += 25.0  # Defensive clear

        # Boost for saves
        if goal_distance < 2000 and player.boost_amount > 40:
            reward += 3.0

        return reward


class FreestyleRewards(BaseRewardFunction):
    """
    Freestyle rewards: All about style points and mechanical flair.
    Focus on air dribbles, flip resets, ceiling shots, and creative goals.
    """
    
    def __init__(self):
        super().__init__()
        self.air_touches = 0
        self.flip_resets = 0
        self.style_points = 0
        
    def reset(self, initial_state: Any) -> None:
        self.air_touches = 0
        self.flip_resets = 0
        self.style_points = 0
        self.episode_data = {}
        
    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0
        
        # Style goal rewards
        if state.last_touch == player.car_id:
            base_goal_reward = 50.0
            style_multiplier = 1.0
            
            # Height bonus
            if player.car_data.position[2] > 500:
                style_multiplier += 1.0
            if player.car_data.position[2] > 1000:
                style_multiplier += 2.0
                
            # Air time bonus
            if self.air_touches > 5:
                style_multiplier += 1.5
                
            # Flip reset bonus
            if self.flip_resets > 0:
                style_multiplier += 2.0
                
            reward += base_goal_reward * style_multiplier
            
        # Air dribble rewards
        ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
        if player.car_data.position[2] > 200 and state.ball.position[2] > 200:
            if ball_distance < 200:
                self.air_touches += 1
                reward += 8.0
                
                # Extended air dribble bonus
                if self.air_touches > 3:
                    reward += 5.0 * (self.air_touches - 3)
                    
        # Flip reset detection
        if (player.car_data.position[2] > 100 and ball_distance < 120 and 
            state.ball.position[2] > player.car_data.position[2] - 50):
            angular_vel = np.linalg.norm(player.car_data.angular_velocity)
            if angular_vel > 3:
                self.flip_resets += 1
                reward += 25.0
                
        # Ceiling shot setup
        if player.car_data.position[2] > 1500:
            reward += 5.0
            if ball_distance < 300:
                reward += 10.0
                
        # Wall play
        if abs(player.car_data.position[0]) > 3500:
            reward += 2.0
            if ball_distance < 200:
                reward += 5.0
                
        # Boost management for freestyle
        if player.boost_amount > 60:
            reward += 2.0  # Need boost for freestyle
            
        return reward


class MechanicalRewards(BaseRewardFunction):
    """
    Mechanical specialist: Good on ground AND air.
    Balanced mechanical skills with smart decision making.
    """
    
    def __init__(self):
        super().__init__()
        self.ground_touches = 0
        self.air_touches = 0
        self.mechanical_plays = 0
        
    def reset(self, initial_state: Any) -> None:
        self.ground_touches = 0
        self.air_touches = 0
        self.mechanical_plays = 0
        self.episode_data = {}
        
    def get_reward(self, player, state, previous_action) -> float:
        reward = 0.0
        
        # Balanced goal rewards
        if state.last_touch == player.car_id:
            reward += 75.0
            
            # Ground play bonus
            if player.car_data.position[2] < 100:
                reward += 25.0  # Ground shot bonus
                
            # Air play bonus
            elif player.car_data.position[2] > 300:
                reward += 35.0  # Aerial shot bonus
                
        ball_distance = np.linalg.norm(player.car_data.position - state.ball.position)
        
        # Ground mechanical skills
        if player.car_data.position[2] < 150:
            if ball_distance < 150:
                self.ground_touches += 1
                reward += 3.0
                
                # Dribbling detection
                speed = np.linalg.norm(player.car_data.linear_velocity)
                if speed > 800 and ball_distance < 120:
                    reward += 5.0  # Dribbling bonus
                    
        # Air mechanical skills
        elif player.car_data.position[2] > 200:
            if ball_distance < 200:
                self.air_touches += 1
                reward += 4.0
                
                # Air dribble bonus
                if state.ball.position[2] > 200:
                    reward += 6.0
                    
        # Boost management
        if 30 < player.boost_amount < 80:
            reward += 1.0  # Good boost management
            
        # Speed control
        speed = np.linalg.norm(player.car_data.linear_velocity)
        if 1200 < speed < 2000:
            reward += 1.0  # Good speed control
            
        return reward
