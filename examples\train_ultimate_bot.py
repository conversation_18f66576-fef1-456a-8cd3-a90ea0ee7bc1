#!/usr/bin/env python3
"""
Example: Train the ULTIMATE bot that can beat any other bot!

This script demonstrates how to train the ultimate bot using the RLGym Framework.
The ultimate bot combines all playstyles and is designed to dominate any opponent.
"""

import sys
import os

# Add the framework to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from rlgym_framework import (
    get_preset_config, 
    RLGymEnvironment, 
    PPOAgent, 
    Trainer,
    get_device_manager,
    setup_logging
)


def main():
    """Train the ultimate bot."""
    print("🚀 RLGym Framework - Ultimate Bot Training")
    print("=" * 50)
    
    # Load the ultimate preset configuration
    print("Loading ULTIMATE bot configuration...")
    config = get_preset_config("ultimate")
    
    # You can customize the configuration if needed
    config.training.total_timesteps = 50_000_000  # 50M timesteps for ultimate performance
    config.training.experiment_name = "ultimate_dominator"
    
    print(f"Bot Name: {config.experiment['name']}")
    print(f"Description: {config.experiment['description']}")
    print(f"Training Timesteps: {config.training.total_timesteps:,}")
    
    # Initialize device manager
    device_manager = get_device_manager()
    print(f"Using device: {device_manager.current_device}")
    
    # Set up logging
    setup_logging(log_dir=config.training.log_dir)
    
    # Create environment
    print("\nCreating RLGym environment...")
    env = RLGymEnvironment(
        env_config=config.environment,
        device_manager=device_manager
    )
    
    print(f"Observation space: {env.observation_space.shape}")
    print(f"Action space: {env.action_space.shape}")
    
    # Create the ultimate agent
    print("\nCreating ULTIMATE PPO agent...")
    agent = PPOAgent(
        observation_size=env.observation_space.shape[-1],
        action_size=env.action_space.shape[-1],
        device_manager=device_manager,
        **config.agent,
        # Training hyperparameters
        learning_rate=config.training.learning_rate,
        hidden_sizes=config.training.hidden_sizes,
        clip_ratio=config.training.clip_ratio,
        entropy_coef=config.training.entropy_coef,
        value_loss_coef=config.training.value_loss_coef,
        max_grad_norm=config.training.max_grad_norm,
        ppo_epochs=config.training.ppo_epochs,
        gamma=config.training.gamma,
        gae_lambda=config.training.gae_lambda,
    )
    
    print(f"Agent network: {agent.networks['actor_critic']}")
    
    # Create trainer
    print("\nCreating trainer...")
    trainer = Trainer(
        agent=agent,
        env=env,
        config=config.training,
        device_manager=device_manager
    )
    
    # Start training
    print("\n" + "=" * 50)
    print("🔥 STARTING ULTIMATE BOT TRAINING! 🔥")
    print("This bot will learn to dominate ANY opponent!")
    print("=" * 50)
    
    try:
        results = trainer.train()
        
        print("\n" + "=" * 50)
        print("🎉 ULTIMATE BOT TRAINING COMPLETED! 🎉")
        print("=" * 50)
        print(f"Total timesteps: {results['total_timesteps']:,}")
        print(f"Episodes completed: {results['episode_count']:,}")
        print(f"Training time: {results['total_time']:.2f} seconds")
        print(f"Final evaluation reward: {results['final_eval']['mean_reward']:.2f}")
        print(f"Model saved to: {config.training.save_dir}")
        
        print("\n🚀 Your ULTIMATE bot is ready to dominate!")
        print("Load it in RLGym and watch it destroy the competition!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Training interrupted by user.")
        print("Saving current progress...")
        trainer._save_model()
        print("✅ Progress saved!")
        
    except Exception as e:
        print(f"\n❌ Error during training: {e}")
        print("Check the logs for more details.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
