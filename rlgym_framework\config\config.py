"""Configuration management for the RLGym Framework."""

import os
import json
import yaml
from typing import Any, Dict, Optional, Union
from pathlib import Path

from ..training.config import TrainingConfig, EvaluationConfig


class Config:
    """
    Main configuration class that handles loading and saving of configurations
    from YAML and JSON files for easy experiment management.
    """
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        self.data = config_dict or {}
        
        # Parse training and evaluation configs
        self.training = TrainingConfig.from_dict(self.data.get('training', {}))
        self.evaluation = EvaluationConfig.from_dict(self.data.get('evaluation', {}))
        
        # Environment configuration
        self.environment = self.data.get('environment', {})
        
        # Agent configuration
        self.agent = self.data.get('agent', {})
        
        # Experiment metadata
        self.experiment = self.data.get('experiment', {})
    
    def update(self, **kwargs) -> None:
        """Update configuration with new values."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                if key in ['training', 'evaluation']:
                    getattr(self, key).update(**value)
                else:
                    setattr(self, key, value)
            else:
                self.data[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'training': self.training.to_dict(),
            'evaluation': self.evaluation.to_dict(),
            'environment': self.environment,
            'agent': self.agent,
            'experiment': self.experiment,
            **{k: v for k, v in self.data.items() 
               if k not in ['training', 'evaluation', 'environment', 'agent', 'experiment']}
        }
    
    def save(self, filepath: Union[str, Path], format: str = 'auto') -> None:
        """
        Save configuration to file.
        
        Args:
            filepath: Path to save configuration
            format: File format ('yaml', 'json', or 'auto' to detect from extension)
        """
        filepath = Path(filepath)
        
        # Auto-detect format from extension
        if format == 'auto':
            if filepath.suffix.lower() in ['.yml', '.yaml']:
                format = 'yaml'
            elif filepath.suffix.lower() == '.json':
                format = 'json'
            else:
                format = 'yaml'  # Default to YAML
        
        # Create directory if it doesn't exist
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Save configuration
        config_dict = self.to_dict()
        
        if format == 'yaml':
            with open(filepath, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
        elif format == 'json':
            with open(filepath, 'w') as f:
                json.dump(config_dict, f, indent=2)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    @classmethod
    def load(cls, filepath: Union[str, Path]) -> 'Config':
        """
        Load configuration from file.
        
        Args:
            filepath: Path to configuration file
            
        Returns:
            Config instance
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"Configuration file not found: {filepath}")
        
        # Determine format from extension
        if filepath.suffix.lower() in ['.yml', '.yaml']:
            with open(filepath, 'r') as f:
                config_dict = yaml.safe_load(f)
        elif filepath.suffix.lower() == '.json':
            with open(filepath, 'r') as f:
                config_dict = json.load(f)
        else:
            raise ValueError(f"Unsupported file format: {filepath.suffix}")
        
        return cls(config_dict)
    
    def merge(self, other: 'Config') -> 'Config':
        """
        Merge this configuration with another configuration.
        
        Args:
            other: Another Config instance
            
        Returns:
            New Config instance with merged values
        """
        merged_dict = self.to_dict()
        other_dict = other.to_dict()
        
        # Deep merge dictionaries
        def deep_merge(dict1: Dict, dict2: Dict) -> Dict:
            result = dict1.copy()
            for key, value in dict2.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        merged_dict = deep_merge(merged_dict, other_dict)
        return Config(merged_dict)
    
    def get_experiment_name(self) -> str:
        """Get experiment name for logging and saving."""
        if 'name' in self.experiment:
            return self.experiment['name']
        
        # Generate name from key parameters
        name_parts = []
        
        if 'algorithm' in self.agent:
            name_parts.append(self.agent['algorithm'])
        
        if 'environment' in self.environment:
            name_parts.append(self.environment['environment'])
        
        name_parts.append(f"lr{self.training.learning_rate}")
        name_parts.append(f"bs{self.training.batch_size}")
        
        return "_".join(name_parts) if name_parts else "experiment"
    
    def validate(self) -> None:
        """Validate the configuration."""
        self.training.validate()
        
        # Additional validation can be added here
        if 'algorithm' in self.agent:
            supported_algorithms = ['ppo', 'sac', 'td3', 'a2c']
            if self.agent['algorithm'].lower() not in supported_algorithms:
                raise ValueError(f"Unsupported algorithm: {self.agent['algorithm']}")


def load_config(filepath: Union[str, Path]) -> Config:
    """
    Load configuration from file.
    
    Args:
        filepath: Path to configuration file
        
    Returns:
        Config instance
    """
    return Config.load(filepath)


def save_config(config: Config, filepath: Union[str, Path], format: str = 'auto') -> None:
    """
    Save configuration to file.
    
    Args:
        config: Config instance to save
        filepath: Path to save configuration
        format: File format ('yaml', 'json', or 'auto')
    """
    config.save(filepath, format)


def create_default_config() -> Config:
    """Create a default configuration for quick start."""
    default_config = {
        'training': {
            'total_timesteps': 1_000_000,
            'rollout_length': 2048,
            'batch_size': 64,
            'learning_rate': 3e-4,
            'save_interval': 100,
            'log_interval': 10,
            'eval_interval': 50,
        },
        'evaluation': {
            'num_episodes': 10,
            'deterministic': True,
        },
        'environment': {
            'environment': 'RLGym',
            'max_episode_length': 1000,
        },
        'agent': {
            'algorithm': 'PPO',
            'hidden_sizes': [256, 256],
            'activation': 'relu',
        },
        'experiment': {
            'name': 'default_experiment',
            'description': 'Default RLGym Framework experiment',
        }
    }
    
    return Config(default_config)


def get_config_template() -> Dict[str, Any]:
    """Get a configuration template with all available options."""
    template = {
        'training': {
            # Basic training settings
            'total_timesteps': 1_000_000,
            'rollout_length': 2048,
            'batch_size': 64,
            'learning_rate': 3e-4,
            'max_episode_length': 1000,
            'num_envs': 1,
            
            # PPO-specific settings
            'ppo_epochs': 10,
            'clip_ratio': 0.2,
            'value_loss_coef': 0.5,
            'entropy_coef': 0.01,
            'max_grad_norm': 0.5,
            'gamma': 0.99,
            'gae_lambda': 0.95,
            
            # Network architecture
            'hidden_sizes': [256, 256],
            'shared_layers': 1,
            'activation': 'relu',
            
            # Device settings
            'device': None,  # Auto-detect
            
            # Logging and saving
            'log_interval': 10,
            'save_interval': 100,
            'eval_interval': 50,
            'eval_episodes': 5,
            'save_dir': './models',
            'log_dir': './logs',
            
            # Monitoring
            'use_wandb': False,
            'use_tensorboard': True,
            'project_name': 'rlgym_framework',
            'experiment_name': None,
        },
        'evaluation': {
            'num_episodes': 10,
            'deterministic': True,
            'render': False,
            'save_video': False,
            'video_dir': './videos',
            'max_episode_length': None,
        },
        'environment': {
            'environment': 'RLGym',
            'game_speed': 100,
            'tick_skip': 8,
            'spawn_opponents': True,
            'team_size': 1,
            'gravity': 1,
            'boost_consumption': 1,
        },
        'agent': {
            'algorithm': 'PPO',
            'observation_size': 107,  # Default for RLGym
            'action_size': 8,         # Default for RLGym
        },
        'experiment': {
            'name': 'my_experiment',
            'description': 'My RLGym Framework experiment',
            'tags': ['rlgym', 'ppo'],
        }
    }
    
    return template
