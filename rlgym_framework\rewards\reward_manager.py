"""
Reward Manager - Automatically handles all reward engineering for different bot styles.

This system completely eliminates the need for users to understand or configure rewards.
Just pick a bot style and the framework handles everything!
"""

from typing import Dict, Any, Optional, Callable
import numpy as np
from abc import ABC, abstractmethod


class BaseRewardFunction(ABC):
    """Base class for all reward functions."""
    
    def __init__(self):
        self.prev_actions = None
        self.prev_state = None
        self.episode_data = {}
        
    @abstractmethod
    def reset(self, initial_state: Any) -> None:
        """Reset reward function for new episode."""
        pass
    
    @abstractmethod
    def get_reward(self, player, state, previous_action) -> float:
        """Calculate reward for current step."""
        pass
    
    def get_final_reward(self) -> float:
        """Get final reward at episode end (optional)."""
        return 0.0


class RewardManager:
    """
    Manages reward calculation for different bot styles.
    Automatically handles all the complex reward engineering.
    """
    
    def __init__(self, bot_style: str):
        self.bot_style = bot_style.lower()
        self.reward_function = self._create_reward_function()
        
    def _create_reward_function(self) -> BaseRewardFunction:
        """Create appropriate reward function for bot style."""
        from .bot_rewards import (
            NextoRewards, OptiRewards, RippleRewards, SeerRewards,
            GroundSpecialistRewards, AerialSpecialistRewards,
            AllAroundRewards, MechanicalRewards, PositioningRewards,
            SpeedDemonRewards, WallPlayRewards, DemoHunterRewards, DefensiveRewards,
            FreestyleRewards, UltimateRewards, GodTierRewards
        )
        
        reward_map = {
            # ULTIMATE TIER
            'ultimate': UltimateRewards,
            'god_tier': GodTierRewards,

            # Pro player styles
            'nexto': NextoRewards,
            'opti': OptiRewards,
            'ripple': RippleRewards,
            'seer': SeerRewards,

            # Specialized styles
            'ground': GroundSpecialistRewards,
            'aerial': AerialSpecialistRewards,
            'all_around': AllAroundRewards,
            'mechanical': MechanicalRewards,
            'freestyle': FreestyleRewards,
            'positioning': PositioningRewards,
            'speed': SpeedDemonRewards,
            'wall_play': WallPlayRewards,
            'demo': DemoHunterRewards,
            'defensive': DefensiveRewards,
        }
        
        if self.bot_style not in reward_map:
            # Default to all-around if unknown style
            return AllAroundRewards()
        
        return reward_map[self.bot_style]()
    
    def reset(self, initial_state: Any) -> None:
        """Reset for new episode."""
        self.reward_function.reset(initial_state)
    
    def get_reward(self, player, state, previous_action) -> float:
        """Get reward for current step."""
        return self.reward_function.get_reward(player, state, previous_action)
    
    def get_final_reward(self) -> float:
        """Get final episode reward."""
        return self.reward_function.get_final_reward()


def get_reward_function(bot_style: str) -> Callable:
    """
    Get a reward function for the specified bot style.
    
    This is the main function users will interact with - it returns
    a ready-to-use reward function that can be plugged into RLGym.
    
    Args:
        bot_style: Style of bot ('nexto', 'ground', 'aerial', etc.)
        
    Returns:
        Reward function compatible with RLGym
    """
    manager = RewardManager(bot_style)
    
    def reward_function(player, state, previous_action):
        return manager.get_reward(player, state, previous_action)
    
    # Add reset method to the function
    reward_function.reset = manager.reset
    reward_function.get_final_reward = manager.get_final_reward
    
    return reward_function


# Utility functions for common reward calculations
def calculate_ball_distance_reward(player_pos: np.ndarray, ball_pos: np.ndarray, 
                                 max_distance: float = 5000.0) -> float:
    """Calculate reward based on distance to ball."""
    distance = np.linalg.norm(player_pos - ball_pos)
    return max(0, 1 - distance / max_distance)


def calculate_goal_distance_reward(player_pos: np.ndarray, goal_pos: np.ndarray,
                                 max_distance: float = 10000.0) -> float:
    """Calculate reward based on distance to goal."""
    distance = np.linalg.norm(player_pos - goal_pos)
    return max(0, 1 - distance / max_distance)


def calculate_speed_reward(velocity: np.ndarray, max_speed: float = 2300.0) -> float:
    """Calculate reward based on speed."""
    speed = np.linalg.norm(velocity)
    return min(1.0, speed / max_speed)


def calculate_ball_touch_reward(player_pos: np.ndarray, ball_pos: np.ndarray,
                               touch_threshold: float = 150.0) -> float:
    """Calculate reward for touching the ball."""
    distance = np.linalg.norm(player_pos - ball_pos)
    if distance < touch_threshold:
        return 1.0
    return 0.0


def calculate_aerial_reward(player_pos: np.ndarray, ball_pos: np.ndarray,
                          min_height: float = 300.0) -> float:
    """Calculate reward for aerial play."""
    player_height = player_pos[2]  # Z coordinate
    ball_height = ball_pos[2]
    
    if player_height > min_height and ball_height > min_height:
        # Both player and ball are in the air
        distance = np.linalg.norm(player_pos - ball_pos)
        height_bonus = min(1.0, (player_height + ball_height) / 2000.0)
        proximity_bonus = max(0, 1 - distance / 1000.0)
        return height_bonus * proximity_bonus
    
    return 0.0


def calculate_save_reward(ball_pos: np.ndarray, ball_vel: np.ndarray,
                         goal_pos: np.ndarray, goal_width: float = 1900.0) -> float:
    """Calculate reward for making saves."""
    # Check if ball is heading towards goal
    goal_direction = goal_pos - ball_pos
    ball_direction = ball_vel / (np.linalg.norm(ball_vel) + 1e-8)
    
    # Dot product to see if ball is moving towards goal
    towards_goal = np.dot(goal_direction, ball_direction)
    
    if towards_goal > 0:
        # Ball is moving towards goal
        distance_to_goal = np.linalg.norm(ball_pos - goal_pos)
        if distance_to_goal < 2000.0:  # Close to goal
            return min(1.0, towards_goal * (2000.0 - distance_to_goal) / 2000.0)
    
    return 0.0


def calculate_demo_reward(player_pos: np.ndarray, opponent_pos: np.ndarray,
                         player_vel: np.ndarray, demo_threshold: float = 1500.0) -> float:
    """Calculate reward for demolitions."""
    distance = np.linalg.norm(player_pos - opponent_pos)
    speed = np.linalg.norm(player_vel)
    
    if distance < 200.0 and speed > demo_threshold:
        # Close to opponent with high speed - potential demo
        return 1.0
    elif distance < 500.0 and speed > demo_threshold * 0.7:
        # Approaching for demo
        return 0.5
    
    return 0.0
