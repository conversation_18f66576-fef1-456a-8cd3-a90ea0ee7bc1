"""
Device management for multi-backend compute support.
Supports DirectML, CUDA, ROCm, and CPU with automatic detection and fallback.
"""

import platform
import warnings
from enum import Enum
from typing import Optional, List, Dict, Any
import torch


class DeviceType(Enum):
    """Supported device types."""
    CPU = "cpu"
    CUDA = "cuda"
    DIRECTML = "directml"
    ROCM = "rocm"


class Device:
    """Represents a compute device with its capabilities."""
    
    def __init__(self, device_type: DeviceType, device_id: int = 0, name: str = ""):
        self.device_type = device_type
        self.device_id = device_id
        self.name = name or f"{device_type.value}:{device_id}"
        self._torch_device = None
        
    @property
    def torch_device(self) -> torch.device:
        """Get the corresponding PyTorch device."""
        if self._torch_device is None:
            if self.device_type == DeviceType.CPU:
                self._torch_device = torch.device("cpu")
            elif self.device_type == DeviceType.CUDA:
                self._torch_device = torch.device(f"cuda:{self.device_id}")
            elif self.device_type == DeviceType.DIRECTML:
                self._torch_device = torch.device("dml")
            elif self.device_type == DeviceType.ROCM:
                self._torch_device = torch.device(f"cuda:{self.device_id}")  # ROCm uses CUDA API
        return self._torch_device
    
    def __str__(self) -> str:
        return self.name
    
    def __repr__(self) -> str:
        return f"Device({self.device_type.value}, {self.device_id}, '{self.name}')"


class DeviceManager:
    """Manages device detection, selection, and optimization."""
    
    def __init__(self, preferred_device: Optional[str] = None):
        self.available_devices: List[Device] = []
        self.preferred_device = preferred_device
        self._detect_devices()
        self.current_device = self._select_best_device()
        
    def _detect_devices(self) -> None:
        """Detect all available compute devices."""
        self.available_devices = []
        
        # Always add CPU
        self.available_devices.append(Device(DeviceType.CPU, 0, "CPU"))
        
        # Detect CUDA devices
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                device_name = torch.cuda.get_device_name(i)
                self.available_devices.append(
                    Device(DeviceType.CUDA, i, f"CUDA:{i} ({device_name})")
                )
        
        # Detect DirectML (Windows only)
        if platform.system() == "Windows":
            try:
                import torch_directml
                if torch_directml.is_available():
                    self.available_devices.append(
                        Device(DeviceType.DIRECTML, 0, "DirectML")
                    )
            except ImportError:
                pass
        
        # Detect ROCm (AMD GPUs on Linux)
        if platform.system() == "Linux" and torch.cuda.is_available():
            # Check if this is actually ROCm (AMD) instead of NVIDIA CUDA
            try:
                device_name = torch.cuda.get_device_name(0).lower()
                if "amd" in device_name or "radeon" in device_name:
                    for i in range(torch.cuda.device_count()):
                        device_name = torch.cuda.get_device_name(i)
                        self.available_devices.append(
                            Device(DeviceType.ROCM, i, f"ROCm:{i} ({device_name})")
                        )
            except Exception:
                pass
    
    def _select_best_device(self) -> Device:
        """Select the best available device based on preferences and performance."""
        if self.preferred_device:
            # Try to find preferred device
            for device in self.available_devices:
                if (self.preferred_device.lower() in device.name.lower() or 
                    self.preferred_device.lower() == device.device_type.value):
                    return device
            warnings.warn(f"Preferred device '{self.preferred_device}' not found. Using auto-selection.")
        
        # Auto-select best device (priority: CUDA > DirectML > ROCm > CPU)
        for device_type in [DeviceType.CUDA, DeviceType.DIRECTML, DeviceType.ROCM, DeviceType.CPU]:
            for device in self.available_devices:
                if device.device_type == device_type:
                    return device
        
        # Fallback to CPU (should always be available)
        return Device(DeviceType.CPU, 0, "CPU")
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get detailed information about the current device."""
        info = {
            "current_device": str(self.current_device),
            "device_type": self.current_device.device_type.value,
            "available_devices": [str(d) for d in self.available_devices],
            "torch_device": str(self.current_device.torch_device),
        }
        
        if self.current_device.device_type == DeviceType.CUDA:
            info.update({
                "cuda_version": torch.version.cuda,
                "memory_allocated": torch.cuda.memory_allocated(self.current_device.device_id),
                "memory_reserved": torch.cuda.memory_reserved(self.current_device.device_id),
                "memory_total": torch.cuda.get_device_properties(self.current_device.device_id).total_memory,
            })
        
        return info
    
    def set_device(self, device_name: str) -> bool:
        """Set the current device by name or type."""
        for device in self.available_devices:
            if (device_name.lower() in device.name.lower() or 
                device_name.lower() == device.device_type.value):
                self.current_device = device
                return True
        return False
    
    def optimize_for_training(self) -> None:
        """Apply device-specific optimizations for training."""
        if self.current_device.device_type == DeviceType.CUDA:
            # Enable cuDNN benchmarking for consistent input sizes
            torch.backends.cudnn.benchmark = True
            # Enable TensorFloat-32 for faster training on Ampere GPUs
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
        
        elif self.current_device.device_type == DeviceType.DIRECTML:
            # DirectML-specific optimizations
            torch.backends.directml.enabled = True
        
        # Set default tensor type for the device
        if self.current_device.device_type != DeviceType.CPU:
            torch.set_default_tensor_type(torch.FloatTensor)
    
    def clear_cache(self) -> None:
        """Clear device memory cache."""
        if self.current_device.device_type in [DeviceType.CUDA, DeviceType.ROCM]:
            torch.cuda.empty_cache()
    
    def to_device(self, tensor_or_model) -> Any:
        """Move tensor or model to the current device."""
        return tensor_or_model.to(self.current_device.torch_device)


# Global device manager instance
_device_manager: Optional[DeviceManager] = None


def get_device_manager(preferred_device: Optional[str] = None) -> DeviceManager:
    """Get the global device manager instance."""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager(preferred_device)
    return _device_manager


def get_current_device() -> Device:
    """Get the current active device."""
    return get_device_manager().current_device


def set_device(device_name: str) -> bool:
    """Set the current device globally."""
    return get_device_manager().set_device(device_name)
