"""Base environment interface for the RLGym Framework."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union
import numpy as np
import torch
from ..core.base import BaseComponent


class BaseEnvironment(BaseComponent, ABC):
    """
    Abstract base class for all environments in the RLGym Framework.
    
    This class provides a standardized interface that's compatible with
    both Gymnasium and RLGym environments while adding device management
    and performance optimizations.
    """
    
    def __init__(self, **kwargs):
        super().__init__()
        self.configure(**kwargs)
        self._episode_length = 0
        self._max_episode_length = kwargs.get('max_episode_length', 1000)
        self._num_agents = kwargs.get('num_agents', 1)
        self._observation_space = None
        self._action_space = None
        
    @property
    def observation_space(self):
        """Get the observation space."""
        return self._observation_space
    
    @property
    def action_space(self):
        """Get the action space."""
        return self._action_space
    
    @property
    def num_agents(self) -> int:
        """Get the number of agents in the environment."""
        return self._num_agents
    
    @property
    def episode_length(self) -> int:
        """Get the current episode length."""
        return self._episode_length
    
    @property
    def max_episode_length(self) -> int:
        """Get the maximum episode length."""
        return self._max_episode_length
    
    @abstractmethod
    def reset(self, seed: Optional[int] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Reset the environment to initial state.
        
        Args:
            seed: Random seed for reproducibility
            
        Returns:
            Tuple of (observations, info_dict)
        """
        self._episode_length = 0
        pass
    
    @abstractmethod
    def step(self, actions: Union[np.ndarray, List[float]]) -> Tuple[
        np.ndarray, Union[float, np.ndarray], bool, bool, Dict[str, Any]
    ]:
        """
        Execute one step in the environment.
        
        Args:
            actions: Actions to execute
            
        Returns:
            Tuple of (observations, rewards, terminated, truncated, info)
        """
        self._episode_length += 1
        pass
    
    @abstractmethod
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """
        Render the environment.
        
        Args:
            mode: Rendering mode ('human', 'rgb_array', etc.)
            
        Returns:
            Rendered frame if mode is 'rgb_array', None otherwise
        """
        pass
    
    def close(self) -> None:
        """Close the environment and clean up resources."""
        pass
    
    def seed(self, seed: int) -> None:
        """Set the random seed for the environment."""
        np.random.seed(seed)
    
    def get_observation_tensor(self, observations: np.ndarray) -> torch.Tensor:
        """
        Convert observations to PyTorch tensor on the current device.
        
        Args:
            observations: Numpy array of observations
            
        Returns:
            PyTorch tensor on the current device
        """
        tensor = torch.from_numpy(observations).float()
        return self.to_device(tensor)
    
    def get_action_array(self, actions: torch.Tensor) -> np.ndarray:
        """
        Convert action tensor to numpy array.
        
        Args:
            actions: PyTorch tensor of actions
            
        Returns:
            Numpy array of actions
        """
        return actions.detach().cpu().numpy()
    
    def is_done(self) -> bool:
        """Check if the episode is done based on episode length."""
        return self._episode_length >= self._max_episode_length
    
    def get_state_dict(self) -> Dict[str, Any]:
        """Get the current state of the environment for saving/loading."""
        return {
            'episode_length': self._episode_length,
            'max_episode_length': self._max_episode_length,
            'num_agents': self._num_agents,
            'config': self.get_config(),
        }
    
    def load_state_dict(self, state_dict: Dict[str, Any]) -> None:
        """Load environment state from a state dictionary."""
        self._episode_length = state_dict.get('episode_length', 0)
        self._max_episode_length = state_dict.get('max_episode_length', 1000)
        self._num_agents = state_dict.get('num_agents', 1)
        if 'config' in state_dict:
            self.configure(**state_dict['config'])
