"""Base classes for framework components."""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from .device import get_device_manager, DeviceManager


class BaseComponent(ABC):
    """Base class for all framework components."""
    
    def __init__(self, device_manager: Optional[DeviceManager] = None):
        self.device_manager = device_manager or get_device_manager()
        self._config: Dict[str, Any] = {}
    
    @property
    def device(self):
        """Get the current device."""
        return self.device_manager.current_device
    
    @property
    def torch_device(self):
        """Get the current PyTorch device."""
        return self.device_manager.current_device.torch_device
    
    def to_device(self, tensor_or_model):
        """Move tensor or model to current device."""
        return self.device_manager.to_device(tensor_or_model)
    
    def configure(self, **kwargs) -> None:
        """Configure the component with parameters."""
        self._config.update(kwargs)
    
    def get_config(self) -> Dict[str, Any]:
        """Get the current configuration."""
        return self._config.copy()
    
    @abstractmethod
    def reset(self) -> None:
        """Reset the component to initial state."""
        pass
