"""Main training loop implementation."""

import os
import time
from typing import Any, Dict, List, Optional, Tuple
import torch
import numpy as np
from tqdm import tqdm

from ..core.base import BaseComponent
from ..environments.base import BaseEnvironment
from ..agents.base import BaseAgent
from .config import TrainingConfig, EvaluationConfig
from .experience import RolloutBuffer
from ..utils.logger import Logger
from ..utils.metrics import MetricsTracker


class Trainer(BaseComponent):
    """
    Main trainer class for RL algorithms.
    
    Provides a simple, beginner-friendly interface for training RL agents
    while maintaining high performance across different compute backends.
    """
    
    def __init__(self,
                 agent: BaseAgent,
                 env: BaseEnvironment,
                 config: TrainingConfig,
                 eval_env: Optional[BaseEnvironment] = None,
                 logger: Optional[Logger] = None,
                 **kwargs):
        super().__init__(**kwargs)
        
        self.agent = agent
        self.env = env
        self.eval_env = eval_env or env
        self.config = config
        
        # Validate configuration
        self.config.validate()
        
        # Set up logging
        self.logger = logger or Logger(
            log_dir=config.log_dir,
            use_wandb=config.use_wandb,
            use_tensorboard=config.use_tensorboard,
            project_name=config.project_name,
            experiment_name=config.experiment_name
        )
        
        # Set up metrics tracking
        self.metrics_tracker = MetricsTracker()
        
        # Set up experience buffer
        self.buffer = RolloutBuffer(
            rollout_length=config.rollout_length,
            observation_size=agent.observation_size,
            action_size=agent.action_size,
            num_envs=config.num_envs,
            device_manager=self.device_manager
        )
        
        # Training state
        self.total_timesteps = 0
        self.episode_count = 0
        self.update_count = 0
        
        # Performance tracking
        self.start_time = None
        self.last_log_time = None
        
        # Create save directory
        os.makedirs(config.save_dir, exist_ok=True)
    
    def train(self) -> Dict[str, Any]:
        """
        Main training loop.
        
        Returns:
            Dictionary containing final training statistics
        """
        self.logger.info("Starting training...")
        self.logger.info(f"Device: {self.device}")
        self.logger.info(f"Total timesteps: {self.config.total_timesteps:,}")
        
        self.start_time = time.time()
        self.last_log_time = self.start_time
        
        # Initialize environment
        obs, _ = self.env.reset()
        
        # Training loop
        with tqdm(total=self.config.total_timesteps, desc="Training") as pbar:
            while self.total_timesteps < self.config.total_timesteps:
                # Collect rollout
                rollout_stats = self._collect_rollout(obs, pbar)
                obs = rollout_stats['final_obs']
                
                # Update agent
                if self.buffer.is_ready():
                    update_stats = self._update_agent()
                    self.update_count += 1
                    
                    # Log training progress
                    if self.update_count % self.config.log_interval == 0:
                        self._log_training_progress(rollout_stats, update_stats)
                    
                    # Evaluate agent
                    if self.update_count % self.config.eval_interval == 0:
                        eval_stats = self._evaluate_agent()
                        self._log_evaluation_results(eval_stats)
                    
                    # Save model
                    if self.update_count % self.config.save_interval == 0:
                        self._save_model()
                    
                    # Clear buffer for next rollout
                    self.buffer.clear()
        
        # Final evaluation and save
        final_eval_stats = self._evaluate_agent()
        self._save_model(final=True)
        
        total_time = time.time() - self.start_time
        self.logger.info(f"Training completed in {total_time:.2f} seconds")
        
        return {
            'total_timesteps': self.total_timesteps,
            'episode_count': self.episode_count,
            'update_count': self.update_count,
            'total_time': total_time,
            'final_eval': final_eval_stats,
        }
    
    def _collect_rollout(self, initial_obs: np.ndarray, pbar: tqdm) -> Dict[str, Any]:
        """Collect a rollout of experiences."""
        self.agent.set_eval_mode()
        
        obs = initial_obs
        episode_rewards = []
        episode_lengths = []
        current_episode_reward = 0
        current_episode_length = 0
        
        for step in range(self.config.rollout_length):
            # Get action from agent
            actions, info = self.agent.get_action(obs, deterministic=False)
            values = info.get('values', np.zeros(1))
            log_probs = info.get('log_probs', np.zeros(1))
            
            # Step environment
            next_obs, rewards, terminated, truncated, env_info = self.env.step(actions)
            done = terminated or truncated
            
            # Store experience
            self.buffer.add(
                observations=torch.from_numpy(obs).float(),
                actions=torch.from_numpy(actions).float(),
                rewards=torch.tensor(rewards).float(),
                values=torch.from_numpy(values).float(),
                log_probs=torch.from_numpy(log_probs).float(),
                dones=torch.tensor(done).bool()
            )
            
            # Update statistics
            current_episode_reward += float(rewards) if np.isscalar(rewards) else float(rewards.sum())
            current_episode_length += 1
            self.total_timesteps += 1
            
            # Handle episode termination
            if done:
                episode_rewards.append(current_episode_reward)
                episode_lengths.append(current_episode_length)
                current_episode_reward = 0
                current_episode_length = 0
                self.episode_count += 1
                
                # Reset environment
                obs, _ = self.env.reset()
            else:
                obs = next_obs
            
            # Update progress bar
            pbar.update(1)
            
            if self.total_timesteps >= self.config.total_timesteps:
                break
        
        return {
            'final_obs': obs,
            'episode_rewards': episode_rewards,
            'episode_lengths': episode_lengths,
            'mean_episode_reward': np.mean(episode_rewards) if episode_rewards else 0.0,
            'mean_episode_length': np.mean(episode_lengths) if episode_lengths else 0.0,
        }

    def _update_agent(self) -> Dict[str, float]:
        """Update agent using collected experiences."""
        self.agent.set_training_mode()

        # Get rollout data
        batch = self.buffer.get_rollout()

        # Update agent
        update_stats = self.agent.update(batch)

        return update_stats

    def _evaluate_agent(self, config: Optional[EvaluationConfig] = None) -> Dict[str, float]:
        """Evaluate agent performance."""
        if config is None:
            config = EvaluationConfig(
                num_episodes=self.config.eval_episodes,
                deterministic=True
            )

        self.agent.set_eval_mode()

        episode_rewards = []
        episode_lengths = []

        for episode in range(config.num_episodes):
            obs, _ = self.eval_env.reset()
            episode_reward = 0
            episode_length = 0
            done = False

            while not done:
                actions, _ = self.agent.get_action(obs, deterministic=config.deterministic)
                obs, rewards, terminated, truncated, _ = self.eval_env.step(actions)

                episode_reward += float(rewards) if np.isscalar(rewards) else float(rewards.sum())
                episode_length += 1
                done = terminated or truncated

                if config.max_episode_length and episode_length >= config.max_episode_length:
                    break

            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)

        return {
            'mean_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'min_reward': np.min(episode_rewards),
            'max_reward': np.max(episode_rewards),
            'mean_length': np.mean(episode_lengths),
            'std_length': np.std(episode_lengths),
        }

    def _log_training_progress(self, rollout_stats: Dict[str, Any], update_stats: Dict[str, float]) -> None:
        """Log training progress."""
        current_time = time.time()
        time_elapsed = current_time - self.start_time
        time_since_last_log = current_time - self.last_log_time

        # Calculate FPS
        fps = self.config.rollout_length / time_since_last_log if time_since_last_log > 0 else 0

        # Prepare log data
        log_data = {
            'timesteps': self.total_timesteps,
            'episodes': self.episode_count,
            'updates': self.update_count,
            'time_elapsed': time_elapsed,
            'fps': fps,
            **rollout_stats,
            **update_stats,
        }

        # Remove non-scalar values for logging
        log_data = {k: v for k, v in log_data.items() if np.isscalar(v)}

        self.logger.log(log_data, step=self.update_count)
        self.metrics_tracker.update(log_data)

        self.last_log_time = current_time

    def _log_evaluation_results(self, eval_stats: Dict[str, float]) -> None:
        """Log evaluation results."""
        eval_data = {f'eval_{k}': v for k, v in eval_stats.items()}
        self.logger.log(eval_data, step=self.update_count)

        self.logger.info(
            f"Evaluation - Mean Reward: {eval_stats['mean_reward']:.2f} ± {eval_stats['std_reward']:.2f}"
        )

    def _save_model(self, final: bool = False) -> None:
        """Save model checkpoint."""
        if final:
            filename = "final_model.pt"
        else:
            filename = f"checkpoint_{self.update_count}.pt"

        filepath = os.path.join(self.config.save_dir, filename)
        self.agent.save(filepath)

        self.logger.info(f"Model saved to {filepath}")

    def reset(self) -> None:
        """Reset trainer state."""
        self.total_timesteps = 0
        self.episode_count = 0
        self.update_count = 0
        self.buffer.reset()
        self.metrics_tracker.reset()

    def load_checkpoint(self, filepath: str) -> None:
        """Load model checkpoint."""
        self.agent.load(filepath)
        self.logger.info(f"Model loaded from {filepath}")

    def get_training_statistics(self) -> Dict[str, Any]:
        """Get current training statistics."""
        return {
            'total_timesteps': self.total_timesteps,
            'episode_count': self.episode_count,
            'update_count': self.update_count,
            'buffer_size': len(self.buffer),
            'device': str(self.device),
            'metrics': self.metrics_tracker.get_summary(),
        }
