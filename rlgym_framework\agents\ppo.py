"""Proximal Policy Optimization (PPO) agent implementation."""

import os
from typing import Any, Dict, List, Optional, Tuple, Union
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np

from .base import BaseAgent
from .networks import ActorCriticNetwork


class PPOAgent(BaseAgent):
    """
    Proximal Policy Optimization (PPO) agent.
    
    A beginner-friendly implementation of PPO that's optimized for performance
    across different compute backends while maintaining simplicity.
    """
    
    def __init__(self,
                 observation_size: int,
                 action_size: int,
                 learning_rate: float = 3e-4,
                 hidden_sizes: List[int] = [256, 256],
                 shared_layers: int = 1,
                 clip_ratio: float = 0.2,
                 value_loss_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5,
                 ppo_epochs: int = 10,
                 mini_batch_size: int = 64,
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 **kwargs):
        """
        Initialize PPO agent.
        
        Args:
            observation_size: Size of observation space
            action_size: Size of action space
            learning_rate: Learning rate for optimizer
            hidden_sizes: Hidden layer sizes for networks
            shared_layers: Number of shared layers between actor and critic
            clip_ratio: PPO clipping ratio
            value_loss_coef: Coefficient for value loss
            entropy_coef: Coefficient for entropy bonus
            max_grad_norm: Maximum gradient norm for clipping
            ppo_epochs: Number of PPO update epochs per batch
            mini_batch_size: Mini-batch size for updates
            gamma: Discount factor
            gae_lambda: GAE lambda parameter
        """
        super().__init__(observation_size, action_size, learning_rate, **kwargs)
        
        # PPO hyperparameters
        self.clip_ratio = clip_ratio
        self.value_loss_coef = value_loss_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.ppo_epochs = ppo_epochs
        self.mini_batch_size = mini_batch_size
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        
        # Create actor-critic network
        self.networks['actor_critic'] = ActorCriticNetwork(
            observation_size=observation_size,
            action_size=action_size,
            hidden_sizes=hidden_sizes,
            shared_layers=shared_layers,
            device_manager=self.device_manager,
            **kwargs
        )
        
        # Create optimizer
        self.optimizers['actor_critic'] = optim.Adam(
            self.networks['actor_critic'].parameters(),
            lr=learning_rate,
            eps=1e-5
        )
        
        # Training metrics
        self.metrics = {
            'policy_loss': 0.0,
            'value_loss': 0.0,
            'entropy_loss': 0.0,
            'total_loss': 0.0,
            'clip_fraction': 0.0,
            'kl_divergence': 0.0,
        }
    
    def get_action(self, 
                   observations: Union[np.ndarray, torch.Tensor],
                   deterministic: bool = False) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Get action for given observations."""
        self.set_eval_mode()
        
        with torch.no_grad():
            obs_tensor = self.preprocess_observations(observations)
            
            if deterministic:
                # Use mean of action distribution
                action_mean, value = self.networks['actor_critic'](obs_tensor)
                actions = action_mean
                log_probs = None
            else:
                # Sample from action distribution
                dist = self.networks['actor_critic'].get_action_distribution(obs_tensor)
                actions = dist.sample()
                log_probs = dist.log_prob(actions).sum(dim=-1, keepdim=True)
                value = self.networks['actor_critic'].get_value(obs_tensor)
            
            # Convert to numpy
            actions_np = self.postprocess_actions(actions)
            
            info = {
                'values': self.to_numpy(value) if value is not None else None,
                'log_probs': self.to_numpy(log_probs) if log_probs is not None else None,
            }
            
            return actions_np, info
    
    def update(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """Update agent using PPO algorithm."""
        self.set_training_mode()
        
        # Extract batch data
        observations = batch['observations']
        actions = batch['actions']
        old_log_probs = batch['log_probs']
        rewards = batch['rewards']
        values = batch['values']
        dones = batch['dones']
        
        # Compute advantages and returns
        with torch.no_grad():
            next_values = torch.cat([values[1:], torch.zeros_like(values[-1:])], dim=0)
            advantages, returns = self.compute_advantages(
                rewards, values, next_values, dones, self.gamma, self.gae_lambda
            )
        
        # PPO update loop
        total_policy_loss = 0.0
        total_value_loss = 0.0
        total_entropy_loss = 0.0
        total_clip_fraction = 0.0
        total_kl_div = 0.0
        
        batch_size = observations.size(0)
        num_mini_batches = max(1, batch_size // self.mini_batch_size)
        
        for epoch in range(self.ppo_epochs):
            # Shuffle data
            indices = torch.randperm(batch_size, device=self.torch_device)
            
            for start_idx in range(0, batch_size, self.mini_batch_size):
                end_idx = min(start_idx + self.mini_batch_size, batch_size)
                mini_batch_indices = indices[start_idx:end_idx]
                
                # Get mini-batch data
                mb_obs = observations[mini_batch_indices]
                mb_actions = actions[mini_batch_indices]
                mb_old_log_probs = old_log_probs[mini_batch_indices]
                mb_advantages = advantages[mini_batch_indices]
                mb_returns = returns[mini_batch_indices]
                
                # Forward pass
                log_probs, values, entropy = self.networks['actor_critic'].evaluate_actions(
                    mb_obs, mb_actions
                )
                
                # Policy loss (PPO clipped objective)
                ratio = torch.exp(log_probs - mb_old_log_probs)
                surr1 = ratio * mb_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_ratio, 1 + self.clip_ratio) * mb_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # Value loss
                value_loss = nn.MSELoss()(values.squeeze(), mb_returns)
                
                # Entropy loss (for exploration)
                entropy_loss = -entropy.mean()
                
                # Total loss
                total_loss = (policy_loss + 
                             self.value_loss_coef * value_loss + 
                             self.entropy_coef * entropy_loss)
                
                # Backward pass
                self.optimizers['actor_critic'].zero_grad()
                total_loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(
                    self.networks['actor_critic'].parameters(),
                    self.max_grad_norm
                )
                
                self.optimizers['actor_critic'].step()
                
                # Accumulate metrics
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy_loss.item()
                
                # Compute additional metrics
                with torch.no_grad():
                    clip_fraction = ((ratio - 1.0).abs() > self.clip_ratio).float().mean()
                    kl_div = (mb_old_log_probs - log_probs).mean()
                    total_clip_fraction += clip_fraction.item()
                    total_kl_div += kl_div.item()
        
        # Update training step
        self.training_step += 1
        
        # Compute average metrics
        num_updates = self.ppo_epochs * num_mini_batches
        self.metrics.update({
            'policy_loss': total_policy_loss / num_updates,
            'value_loss': total_value_loss / num_updates,
            'entropy_loss': total_entropy_loss / num_updates,
            'total_loss': (total_policy_loss + total_value_loss + total_entropy_loss) / num_updates,
            'clip_fraction': total_clip_fraction / num_updates,
            'kl_divergence': total_kl_div / num_updates,
        })
        
        return self.metrics.copy()
    
    def save(self, filepath: str) -> None:
        """Save agent state to file."""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        torch.save(self.get_state_dict(), filepath)
    
    def load(self, filepath: str) -> None:
        """Load agent state from file."""
        state_dict = torch.load(filepath, map_location=self.torch_device)
        self.load_state_dict(state_dict)
    
    def get_training_metrics(self) -> Dict[str, Any]:
        """Get current training metrics."""
        base_metrics = super().get_training_metrics()
        base_metrics.update(self.metrics)
        return base_metrics
