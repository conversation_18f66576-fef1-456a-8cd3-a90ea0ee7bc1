# RLGym Framework - The Ultimate Bot Training Cheat Code! 🚀

**The easiest, fastest, and most powerful way to train Rocket League bots!**

No more complex configurations, reward engineering, or technical headaches. Just pick a bot style and watch it dominate!

## 🎯 What Makes This Special?

- **Zero Configuration Required** - Just pick a preset and go!
- **Multi-Backend Support** - Works with DirectML, CUDA, ROCm, and CPU
- **Pro Player Inspired Bots** - Train bots like <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Seer
- **Ultimate Performance** - Built for speed and efficiency
- **Beginner Friendly** - No RL knowledge needed!

## 🚀 Quick Start - Train Your First Bot in 30 Seconds!

```bash
# Install the framework
pip install -r requirements.txt

# Train a Nexto-style mechanical beast
python -m rlgym_framework.easy_train --preset nexto

# Train an Opti-style positioning master  
python -m rlgym_framework.easy_train --preset opti

# Train the ULTIMATE bot that beats everything
python -m rlgym_framework.easy_train --preset ultimate
```

That's it! No configuration files, no reward engineering, no headaches!

## 🤖 Available Bot Styles

### 🏆 ULTIMATE TIER (The Cheat Codes)
- **`ultimate`** - Combines ALL playstyles, beats any bot
- **`god_tier`** - Transcends normal limitations, inhuman performance

### 👑 Pro Player Inspired
- **`nexto`** - Mechanical genius, air dribbles, flip resets
- **`opti`** - Positioning master, game sense, boost efficiency  
- **`ripple`** - Creative mechanical plays, ceiling shots
- **`seer`** - Defensive wall, incredible saves

### 🎯 Specialized Styles
- **`ground`** - Dribbling master, flicks, 50/50s
- **`aerial`** - Air control specialist, ceiling shots
- **`mechanical`** - Good on ground AND air
- **`freestyle`** - Style points, air dribbles, creativity
- **`positioning`** - Smart rotations, game sense
- **`speed`** - Fast-paced, high-tempo pressure
- **`wall_play`** - Wall shots and control
- **`demo`** - Aggressive demolition hunter
- **`defensive`** - Save specialist, clears

## 💻 System Requirements

- **Windows**: DirectML support (automatic)
- **Linux**: CUDA or ROCm support (automatic detection)
- **macOS**: CPU training (still fast!)
- **Python**: 3.8+ required

## 🎮 Usage Examples

### Basic Training
```bash
# List all available bot styles
python -m rlgym_framework.easy_train --list-presets

# Train with custom name
python -m rlgym_framework.easy_train --preset nexto --name "MyMechanicalBot"

# Train for specific timesteps
python -m rlgym_framework.easy_train --preset ultimate --timesteps 100000000
```

### Advanced Usage
```bash
# Force specific device
python -m rlgym_framework.easy_train --preset god_tier --device cuda

# Enable Weights & Biases logging
python -m rlgym_framework.easy_train --preset opti --wandb

# Custom save directory
python -m rlgym_framework.easy_train --preset ripple --save-dir ./my_models
```

### Python API
```python
from rlgym_framework import get_preset_config, Trainer, RLGymEnvironment, PPOAgent

# Load a preset configuration
config = get_preset_config("ultimate")

# Create environment and agent
env = RLGymEnvironment()
agent = PPOAgent(
    observation_size=env.observation_space.shape[-1],
    action_size=env.action_space.shape[-1]
)

# Train the bot
trainer = Trainer(agent, env, config.training)
results = trainer.train()
```

## 🏗️ Architecture

The framework is built with modularity and performance in mind:

```
rlgym_framework/
├── core/           # Device management, base classes
├── environments/   # RLGym integration, environment wrappers  
├── agents/         # Neural networks, RL algorithms
├── training/       # Training loops, experience collection
├── rewards/        # Automatic reward engineering
├── presets/        # Bot style configurations
├── config/         # Configuration management
└── utils/          # Logging, metrics, utilities
```

## 🎯 Bot Style Comparison

| Style | Mechanics | Positioning | Speed | Defense | Best For |
|-------|-----------|-------------|-------|---------|----------|
| **Ultimate** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Beating everything |
| **God Tier** | ⭐⭐⭐⭐⭐+ | ⭐⭐⭐⭐⭐+ | ⭐⭐⭐⭐⭐+ | ⭐⭐⭐⭐⭐+ | Transcending reality |
| **Nexto** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | Mechanical plays |
| **Opti** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | Smart gameplay |
| **Ripple** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ | ⭐⭐ | Creative shots |
| **Seer** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ | Defensive play |

## 🔧 Advanced Configuration

While presets work out of the box, you can customize everything:

```python
from rlgym_framework import Config

# Start with a preset
config = get_preset_config("nexto")

# Customize training parameters
config.training.total_timesteps = 50_000_000
config.training.learning_rate = 1e-4

# Customize network architecture
config.agent.hidden_sizes = [1024, 512, 256]

# Save custom configuration
config.save("my_custom_config.yaml")
```

## 📊 Monitoring and Logging

Built-in support for:
- **TensorBoard** - Real-time training metrics
- **Weights & Biases** - Experiment tracking
- **Console Logging** - Progress monitoring
- **File Logging** - Detailed logs

## 🚀 Performance Tips

1. **Use Ultimate or God Tier** for maximum performance
2. **Enable GPU acceleration** (automatic detection)
3. **Increase batch size** for faster training on powerful hardware
4. **Use multiple environments** for parallel training
5. **Monitor boost management** - it's crucial for good bots!

## 🤝 Contributing

Want to add your own bot style? It's easy!

1. Create a new reward function in `rewards/bot_rewards.py`
2. Add a preset configuration in `presets/bot_presets.py`
3. Update the registry and you're done!

## 📝 License

MIT License - Use it, modify it, dominate with it!

## 🎉 Credits

Built with love for the Rocket League community. Special thanks to:
- RLGym team for the amazing environment
- Pro players who inspired the bot styles
- The RL community for endless creativity

---

**Ready to dominate? Pick a preset and start training! 🚀**
