"""Metrics tracking utilities."""

import time
from typing import Any, Dict, List, Optional, Union
import numpy as np
from collections import defaultdict, deque


class MetricsTracker:
    """
    Tracks and computes statistics for training metrics.
    """
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.metrics = defaultdict(lambda: deque(maxlen=window_size))
        self.all_time_metrics = defaultdict(list)
        self.start_time = time.time()
        self.last_update_time = self.start_time
    
    def update(self, metrics: Dict[str, Union[int, float]]):
        """
        Update metrics with new values.
        
        Args:
            metrics: Dictionary of metric names to values
        """
        current_time = time.time()
        
        for name, value in metrics.items():
            if isinstance(value, (int, float)) and not np.isnan(value):
                self.metrics[name].append(value)
                self.all_time_metrics[name].append(value)
        
        self.last_update_time = current_time
    
    def get_current(self, metric_name: str) -> Optional[float]:
        """Get the most recent value for a metric."""
        if metric_name in self.metrics and self.metrics[metric_name]:
            return self.metrics[metric_name][-1]
        return None
    
    def get_mean(self, metric_name: str, window: bool = True) -> Optional[float]:
        """
        Get mean value for a metric.
        
        Args:
            metric_name: Name of the metric
            window: If True, use windowed mean; if False, use all-time mean
        """
        data = self.metrics[metric_name] if window else self.all_time_metrics[metric_name]
        if data:
            return np.mean(data)
        return None
    
    def get_std(self, metric_name: str, window: bool = True) -> Optional[float]:
        """
        Get standard deviation for a metric.
        
        Args:
            metric_name: Name of the metric
            window: If True, use windowed std; if False, use all-time std
        """
        data = self.metrics[metric_name] if window else self.all_time_metrics[metric_name]
        if data and len(data) > 1:
            return np.std(data)
        return None
    
    def get_min(self, metric_name: str, window: bool = True) -> Optional[float]:
        """Get minimum value for a metric."""
        data = self.metrics[metric_name] if window else self.all_time_metrics[metric_name]
        if data:
            return np.min(data)
        return None
    
    def get_max(self, metric_name: str, window: bool = True) -> Optional[float]:
        """Get maximum value for a metric."""
        data = self.metrics[metric_name] if window else self.all_time_metrics[metric_name]
        if data:
            return np.max(data)
        return None
    
    def get_summary(self, metric_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get summary statistics for metrics.
        
        Args:
            metric_name: Specific metric to summarize, or None for all metrics
        """
        if metric_name:
            metrics_to_process = [metric_name]
        else:
            metrics_to_process = list(self.metrics.keys())
        
        summary = {}
        for name in metrics_to_process:
            if name in self.metrics and self.metrics[name]:
                summary[name] = {
                    'current': self.get_current(name),
                    'mean': self.get_mean(name),
                    'std': self.get_std(name),
                    'min': self.get_min(name),
                    'max': self.get_max(name),
                    'count': len(self.metrics[name])
                }
        
        # Add timing information
        current_time = time.time()
        summary['_timing'] = {
            'total_time': current_time - self.start_time,
            'time_since_last_update': current_time - self.last_update_time,
        }
        
        return summary
    
    def reset(self):
        """Reset all metrics."""
        self.metrics.clear()
        self.all_time_metrics.clear()
        self.start_time = time.time()
        self.last_update_time = self.start_time
    
    def get_metric_names(self) -> List[str]:
        """Get list of all tracked metric names."""
        return list(self.metrics.keys())
    
    def has_metric(self, metric_name: str) -> bool:
        """Check if a metric is being tracked."""
        return metric_name in self.metrics and len(self.metrics[metric_name]) > 0


class PerformanceMonitor:
    """
    Monitor performance metrics like FPS, memory usage, etc.
    """
    
    def __init__(self):
        self.step_times = deque(maxlen=100)
        self.last_step_time = time.time()
        self.total_steps = 0
    
    def step(self):
        """Record a training step."""
        current_time = time.time()
        if self.total_steps > 0:  # Skip first step
            step_time = current_time - self.last_step_time
            self.step_times.append(step_time)
        
        self.last_step_time = current_time
        self.total_steps += 1
    
    def get_fps(self) -> float:
        """Get current frames per second."""
        if not self.step_times:
            return 0.0
        
        mean_step_time = np.mean(self.step_times)
        return 1.0 / mean_step_time if mean_step_time > 0 else 0.0
    
    def get_step_time_stats(self) -> Dict[str, float]:
        """Get step time statistics."""
        if not self.step_times:
            return {'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0}
        
        return {
            'mean': np.mean(self.step_times),
            'std': np.std(self.step_times),
            'min': np.min(self.step_times),
            'max': np.max(self.step_times),
        }
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get memory usage statistics."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                'percent': process.memory_percent(),
            }
        except ImportError:
            return {'rss_mb': 0.0, 'vms_mb': 0.0, 'percent': 0.0}
    
    def get_gpu_memory_usage(self) -> Dict[str, float]:
        """Get GPU memory usage if available."""
        try:
            import torch
            if torch.cuda.is_available():
                return {
                    'allocated_mb': torch.cuda.memory_allocated() / 1024 / 1024,
                    'reserved_mb': torch.cuda.memory_reserved() / 1024 / 1024,
                    'max_allocated_mb': torch.cuda.max_memory_allocated() / 1024 / 1024,
                }
        except ImportError:
            pass
        
        return {'allocated_mb': 0.0, 'reserved_mb': 0.0, 'max_allocated_mb': 0.0}
