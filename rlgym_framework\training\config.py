"""Training configuration classes."""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union


@dataclass
class TrainingConfig:
    """Configuration for training parameters."""
    
    # Environment settings
    max_episode_length: int = 1000
    num_envs: int = 1
    
    # Training settings
    total_timesteps: int = 1_000_000
    rollout_length: int = 2048
    batch_size: int = 64
    learning_rate: float = 3e-4
    
    # Algorithm-specific settings (PPO)
    ppo_epochs: int = 10
    clip_ratio: float = 0.2
    value_loss_coef: float = 0.5
    entropy_coef: float = 0.01
    max_grad_norm: float = 0.5
    gamma: float = 0.99
    gae_lambda: float = 0.95
    
    # Network architecture
    hidden_sizes: List[int] = field(default_factory=lambda: [256, 256])
    shared_layers: int = 1
    activation: str = "relu"
    
    # Device settings
    device: Optional[str] = None  # Auto-detect if None
    
    # Logging and saving
    log_interval: int = 10
    save_interval: int = 100
    eval_interval: int = 50
    eval_episodes: int = 5
    
    # Paths
    save_dir: str = "./models"
    log_dir: str = "./logs"
    
    # Monitoring
    use_wandb: bool = False
    use_tensorboard: bool = True
    project_name: str = "rlgym_framework"
    experiment_name: Optional[str] = None
    
    # Performance settings
    num_workers: int = 1
    pin_memory: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TrainingConfig':
        """Create config from dictionary."""
        return cls(**config_dict)
    
    def update(self, **kwargs) -> None:
        """Update configuration with new values."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"Unknown configuration parameter: {key}")
    
    def validate(self) -> None:
        """Validate configuration parameters."""
        if self.total_timesteps <= 0:
            raise ValueError("total_timesteps must be positive")
        
        if self.rollout_length <= 0:
            raise ValueError("rollout_length must be positive")
        
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        
        if self.learning_rate <= 0:
            raise ValueError("learning_rate must be positive")
        
        if not 0 < self.gamma <= 1:
            raise ValueError("gamma must be in (0, 1]")
        
        if not 0 <= self.gae_lambda <= 1:
            raise ValueError("gae_lambda must be in [0, 1]")
        
        if self.clip_ratio <= 0:
            raise ValueError("clip_ratio must be positive")
        
        if len(self.hidden_sizes) == 0:
            raise ValueError("hidden_sizes cannot be empty")
        
        if any(size <= 0 for size in self.hidden_sizes):
            raise ValueError("All hidden sizes must be positive")


@dataclass
class EvaluationConfig:
    """Configuration for evaluation parameters."""
    
    num_episodes: int = 10
    deterministic: bool = True
    render: bool = False
    save_video: bool = False
    video_dir: str = "./videos"
    max_episode_length: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'EvaluationConfig':
        """Create config from dictionary."""
        return cls(**config_dict)
