"""RLGym-compatible environment wrapper."""

from typing import Any, Dict, List, Optional, Tuple, Union
import numpy as np
import gymnasium as gym
from .base import BaseEnvironment

try:
    import rlgym
    RLGYM_AVAILABLE = True
except ImportError:
    RLGYM_AVAILABLE = False


class RLGymEnvironment(BaseEnvironment):
    """
    Wrapper for RLGym environments that provides device management
    and performance optimizations while maintaining compatibility.
    """
    
    def __init__(self, 
                 env_config: Optional[Dict[str, Any]] = None,
                 rlgym_env: Optional[Any] = None,
                 **kwargs):
        """
        Initialize RLGym environment wrapper.
        
        Args:
            env_config: Configuration for creating RLGym environment
            rlgym_env: Pre-created RLGym environment instance
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        
        if not RLGYM_AVAILABLE and rlgym_env is None:
            raise ImportError("RLGym is not installed. Install with: pip install rlgym")
        
        if rlgym_env is not None:
            self.env = rlgym_env
        elif env_config is not None:
            self.env = self._create_rlgym_env(env_config)
        else:
            # Create default RLGym environment
            self.env = self._create_default_env()
        
        # Set up spaces
        self._setup_spaces()
        
        # Performance optimizations
        self._obs_buffer = None
        self._action_buffer = None
        
    def _create_rlgym_env(self, config: Dict[str, Any]):
        """Create RLGym environment from configuration."""
        if not RLGYM_AVAILABLE:
            raise ImportError("RLGym is not installed")
        
        # Default RLGym configuration
        default_config = {
            'game_speed': 100,
            'tick_skip': 8,
            'spawn_opponents': True,
            'team_size': 1,
            'gravity': 1,
            'boost_consumption': 1,
        }
        default_config.update(config)
        
        return rlgym.make(**default_config)
    
    def _create_default_env(self):
        """Create a default RLGym environment."""
        if not RLGYM_AVAILABLE:
            # Create a mock environment for testing without RLGym
            return MockRLGymEnv()
        
        return rlgym.make()
    
    def _setup_spaces(self):
        """Set up observation and action spaces."""
        if hasattr(self.env, 'observation_space'):
            self._observation_space = self.env.observation_space
        else:
            # Estimate from first reset
            obs, _ = self.env.reset()
            if isinstance(obs, list):
                obs_shape = (len(obs), len(obs[0]) if obs else 0)
            else:
                obs_shape = obs.shape
            self._observation_space = gym.spaces.Box(
                low=-np.inf, high=np.inf, shape=obs_shape, dtype=np.float32
            )
        
        if hasattr(self.env, 'action_space'):
            self._action_space = self.env.action_space
        else:
            # Default action space for Rocket League (8 actions per agent)
            action_shape = (self.num_agents, 8)
            self._action_space = gym.spaces.Box(
                low=-1.0, high=1.0, shape=action_shape, dtype=np.float32
            )
    
    def reset(self, seed: Optional[int] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Reset the environment."""
        super().reset(seed)
        
        if seed is not None:
            self.seed(seed)
        
        obs = self.env.reset()
        
        # Handle different RLGym return formats
        if isinstance(obs, tuple):
            obs, info = obs
        else:
            info = {}
        
        # Ensure observations are numpy arrays
        if isinstance(obs, list):
            obs = np.array(obs, dtype=np.float32)
        elif not isinstance(obs, np.ndarray):
            obs = np.array([obs], dtype=np.float32)
        
        # Cache observation buffer for performance
        if self._obs_buffer is None or self._obs_buffer.shape != obs.shape:
            self._obs_buffer = np.zeros_like(obs)
        
        return obs, info
    
    def step(self, actions: Union[np.ndarray, List[float]]) -> Tuple[
        np.ndarray, Union[float, np.ndarray], bool, bool, Dict[str, Any]
    ]:
        """Execute one step in the environment."""
        super().step(actions)
        
        # Convert actions to appropriate format
        if isinstance(actions, np.ndarray):
            actions = actions.tolist()
        
        # Execute step in RLGym environment
        result = self.env.step(actions)
        
        # Handle different RLGym return formats
        if len(result) == 4:
            obs, rewards, done, info = result
            truncated = False
        elif len(result) == 5:
            obs, rewards, done, truncated, info = result
        else:
            raise ValueError(f"Unexpected step return format: {len(result)} elements")
        
        # Ensure observations are numpy arrays
        if isinstance(obs, list):
            obs = np.array(obs, dtype=np.float32)
        elif not isinstance(obs, np.ndarray):
            obs = np.array([obs], dtype=np.float32)
        
        # Ensure rewards are proper format
        if isinstance(rewards, list):
            rewards = np.array(rewards, dtype=np.float32)
        elif not isinstance(rewards, (int, float, np.ndarray)):
            rewards = float(rewards)
        
        # Check for episode termination
        terminated = done or self.is_done()
        
        return obs, rewards, terminated, truncated, info
    
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """Render the environment."""
        if hasattr(self.env, 'render'):
            return self.env.render(mode=mode)
        return None
    
    def close(self) -> None:
        """Close the environment."""
        if hasattr(self.env, 'close'):
            self.env.close()
        super().close()


class MockRLGymEnv:
    """Mock RLGym environment for testing without RLGym installation."""
    
    def __init__(self):
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(1, 107), dtype=np.float32
        )
        self.action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(1, 8), dtype=np.float32
        )
        self._step_count = 0
    
    def reset(self):
        self._step_count = 0
        obs = np.random.randn(1, 107).astype(np.float32)
        return obs, {}
    
    def step(self, actions):
        self._step_count += 1
        obs = np.random.randn(1, 107).astype(np.float32)
        reward = np.random.randn(1).astype(np.float32)
        done = self._step_count >= 1000
        info = {'step_count': self._step_count}
        return obs, reward, done, False, info
    
    def render(self, mode="human"):
        return None
    
    def close(self):
        pass
