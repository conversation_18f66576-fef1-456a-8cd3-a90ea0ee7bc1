"""Base agent interface for RL algorithms."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union
import torch
import numpy as np
from ..core.base import BaseComponent


class BaseAgent(BaseComponent, ABC):
    """
    Abstract base class for all RL agents.
    
    Provides a standardized interface for training and inference
    with automatic device management and performance optimizations.
    """
    
    def __init__(self, 
                 observation_size: int,
                 action_size: int,
                 learning_rate: float = 3e-4,
                 **kwargs):
        super().__init__(**kwargs)
        
        self.observation_size = observation_size
        self.action_size = action_size
        self.learning_rate = learning_rate
        
        # Training state
        self.training_step = 0
        self.episode_count = 0
        
        # Networks (to be implemented by subclasses)
        self.networks = {}
        self.optimizers = {}
        
        # Configure agent
        self.configure(**kwargs)
    
    @abstractmethod
    def get_action(self, 
                   observations: Union[np.ndarray, torch.Tensor],
                   deterministic: bool = False) -> <PERSON><PERSON>[np.ndar<PERSON>, Dict[str, Any]]:
        """
        Get action(s) for given observation(s).
        
        Args:
            observations: Environment observations
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (actions, info_dict)
        """
        pass
    
    @abstractmethod
    def update(self, 
               batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        Update agent parameters using a batch of experience.
        
        Args:
            batch: Dictionary containing experience data
            
        Returns:
            Dictionary of training metrics
        """
        pass
    
    @abstractmethod
    def save(self, filepath: str) -> None:
        """Save agent state to file."""
        pass
    
    @abstractmethod
    def load(self, filepath: str) -> None:
        """Load agent state from file."""
        pass
    
    def reset(self) -> None:
        """Reset agent to initial state."""
        self.training_step = 0
        self.episode_count = 0
    
    def set_training_mode(self, training: bool = True) -> None:
        """Set training mode for all networks."""
        for network in self.networks.values():
            if hasattr(network, 'train'):
                network.train(training)
    
    def set_eval_mode(self) -> None:
        """Set evaluation mode for all networks."""
        self.set_training_mode(False)
    
    def to_tensor(self, 
                  data: Union[np.ndarray, List, torch.Tensor],
                  dtype: torch.dtype = torch.float32) -> torch.Tensor:
        """Convert data to PyTorch tensor on current device."""
        if isinstance(data, torch.Tensor):
            tensor = data.to(dtype)
        elif isinstance(data, np.ndarray):
            tensor = torch.from_numpy(data).to(dtype)
        else:
            tensor = torch.tensor(data, dtype=dtype)
        
        return self.to_device(tensor)
    
    def to_numpy(self, tensor: torch.Tensor) -> np.ndarray:
        """Convert tensor to numpy array."""
        return tensor.detach().cpu().numpy()
    
    def get_state_dict(self) -> Dict[str, Any]:
        """Get complete agent state for saving."""
        state_dict = {
            'training_step': self.training_step,
            'episode_count': self.episode_count,
            'config': self.get_config(),
            'networks': {},
            'optimizers': {},
        }
        
        # Save network states
        for name, network in self.networks.items():
            if hasattr(network, 'state_dict'):
                state_dict['networks'][name] = network.state_dict()
        
        # Save optimizer states
        for name, optimizer in self.optimizers.items():
            if hasattr(optimizer, 'state_dict'):
                state_dict['optimizers'][name] = optimizer.state_dict()
        
        return state_dict
    
    def load_state_dict(self, state_dict: Dict[str, Any]) -> None:
        """Load agent state from state dictionary."""
        self.training_step = state_dict.get('training_step', 0)
        self.episode_count = state_dict.get('episode_count', 0)
        
        if 'config' in state_dict:
            self.configure(**state_dict['config'])
        
        # Load network states
        if 'networks' in state_dict:
            for name, network_state in state_dict['networks'].items():
                if name in self.networks and hasattr(self.networks[name], 'load_state_dict'):
                    self.networks[name].load_state_dict(network_state)
        
        # Load optimizer states
        if 'optimizers' in state_dict:
            for name, optimizer_state in state_dict['optimizers'].items():
                if name in self.optimizers and hasattr(self.optimizers[name], 'load_state_dict'):
                    self.optimizers[name].load_state_dict(optimizer_state)
    
    def get_training_metrics(self) -> Dict[str, Any]:
        """Get current training metrics."""
        return {
            'training_step': self.training_step,
            'episode_count': self.episode_count,
            'learning_rate': self.learning_rate,
        }
    
    def preprocess_observations(self, observations: Union[np.ndarray, torch.Tensor]) -> torch.Tensor:
        """Preprocess observations before feeding to networks."""
        tensor = self.to_tensor(observations)
        
        # Ensure correct shape (batch_size, observation_size)
        if tensor.dim() == 1:
            tensor = tensor.unsqueeze(0)
        elif tensor.dim() > 2:
            tensor = tensor.view(tensor.size(0), -1)
        
        return tensor
    
    def postprocess_actions(self, actions: torch.Tensor) -> np.ndarray:
        """Postprocess actions before returning to environment."""
        # Ensure actions are in correct range and format
        actions = torch.clamp(actions, -1.0, 1.0)
        return self.to_numpy(actions)
    
    def compute_advantages(self, 
                          rewards: torch.Tensor,
                          values: torch.Tensor,
                          next_values: torch.Tensor,
                          dones: torch.Tensor,
                          gamma: float = 0.99,
                          gae_lambda: float = 0.95) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute advantages using Generalized Advantage Estimation (GAE).
        
        Args:
            rewards: Reward tensor
            values: Value estimates
            next_values: Next state value estimates
            dones: Done flags
            gamma: Discount factor
            gae_lambda: GAE lambda parameter
            
        Returns:
            Tuple of (advantages, returns)
        """
        deltas = rewards + gamma * next_values * (1 - dones) - values
        advantages = torch.zeros_like(rewards)
        
        advantage = 0
        for t in reversed(range(len(rewards))):
            advantage = deltas[t] + gamma * gae_lambda * (1 - dones[t]) * advantage
            advantages[t] = advantage
        
        returns = advantages + values
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        return advantages, returns
