"""
RLGym Framework - A fast, beginner-friendly reinforcement learning framework
with multi-backend support (DirectML, CUDA, ROCm, CPU).

This framework provides:
- Easy-to-use RL training with RLGym compatibility
- Automatic device detection and optimization
- Modular architecture for extensibility
- Comprehensive logging and monitoring
"""

__version__ = "0.1.0"
__author__ = "RLGym Framework Team"

# Core imports
from .core import Device, DeviceManager
from .environments import BaseEnvironment, RLGymEnvironment
from .agents import BaseAgent, PPOAgent
from .training import Trainer, TrainingConfig
from .config import Config, load_config

# Make key classes available at package level
__all__ = [
    "Device",
    "DeviceManager", 
    "BaseEnvironment",
    "RLGymEnvironment",
    "BaseAgent",
    "PPOAgent",
    "Trainer",
    "TrainingConfig",
    "Config",
    "load_config",
]
