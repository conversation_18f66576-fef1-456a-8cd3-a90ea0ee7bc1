"""Experience buffer for collecting and managing training data."""

from typing import Any, Dict, List, Optional, Tuple
import torch
import numpy as np
from ..core.base import BaseComponent


class ExperienceBuffer(BaseComponent):
    """
    Efficient experience buffer for collecting and batching RL training data.
    Optimized for performance across different compute backends.
    """
    
    def __init__(self, 
                 buffer_size: int,
                 observation_size: int,
                 action_size: int,
                 num_envs: int = 1,
                 **kwargs):
        super().__init__(**kwargs)
        
        self.buffer_size = buffer_size
        self.observation_size = observation_size
        self.action_size = action_size
        self.num_envs = num_envs
        
        # Initialize buffers
        self.reset()
    
    def reset(self) -> None:
        """Reset the buffer to empty state."""
        self.ptr = 0
        self.size = 0
        
        # Pre-allocate tensors for efficiency
        self.observations = torch.zeros(
            (self.buffer_size, self.num_envs, self.observation_size),
            dtype=torch.float32,
            device=self.torch_device
        )
        self.actions = torch.zeros(
            (self.buffer_size, self.num_envs, self.action_size),
            dtype=torch.float32,
            device=self.torch_device
        )
        self.rewards = torch.zeros(
            (self.buffer_size, self.num_envs),
            dtype=torch.float32,
            device=self.torch_device
        )
        self.values = torch.zeros(
            (self.buffer_size, self.num_envs),
            dtype=torch.float32,
            device=self.torch_device
        )
        self.log_probs = torch.zeros(
            (self.buffer_size, self.num_envs),
            dtype=torch.float32,
            device=self.torch_device
        )
        self.dones = torch.zeros(
            (self.buffer_size, self.num_envs),
            dtype=torch.bool,
            device=self.torch_device
        )
    
    def add(self,
            observations: torch.Tensor,
            actions: torch.Tensor,
            rewards: torch.Tensor,
            values: torch.Tensor,
            log_probs: torch.Tensor,
            dones: torch.Tensor) -> None:
        """
        Add experience to the buffer.
        
        Args:
            observations: Environment observations
            actions: Actions taken
            rewards: Rewards received
            values: Value estimates
            log_probs: Log probabilities of actions
            dones: Episode termination flags
        """
        # Ensure tensors are on correct device
        observations = self.to_device(observations)
        actions = self.to_device(actions)
        rewards = self.to_device(rewards)
        values = self.to_device(values)
        log_probs = self.to_device(log_probs)
        dones = self.to_device(dones)
        
        # Handle single environment case
        if observations.dim() == 1:
            observations = observations.unsqueeze(0)
        if actions.dim() == 1:
            actions = actions.unsqueeze(0)
        if rewards.dim() == 0:
            rewards = rewards.unsqueeze(0)
        if values.dim() == 0:
            values = values.unsqueeze(0)
        if log_probs.dim() == 0:
            log_probs = log_probs.unsqueeze(0)
        if dones.dim() == 0:
            dones = dones.unsqueeze(0)
        
        # Store in buffer
        self.observations[self.ptr] = observations
        self.actions[self.ptr] = actions
        self.rewards[self.ptr] = rewards
        self.values[self.ptr] = values
        self.log_probs[self.ptr] = log_probs
        self.dones[self.ptr] = dones
        
        # Update pointers
        self.ptr = (self.ptr + 1) % self.buffer_size
        self.size = min(self.size + 1, self.buffer_size)
    
    def get_batch(self, batch_size: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """
        Get a batch of experiences for training.
        
        Args:
            batch_size: Size of batch to return. If None, returns all data.
            
        Returns:
            Dictionary containing batch data
        """
        if self.size == 0:
            raise ValueError("Buffer is empty")
        
        if batch_size is None or batch_size >= self.size:
            # Return all data
            end_idx = self.size
            batch_data = {
                'observations': self.observations[:end_idx].view(-1, self.observation_size),
                'actions': self.actions[:end_idx].view(-1, self.action_size),
                'rewards': self.rewards[:end_idx].view(-1),
                'values': self.values[:end_idx].view(-1),
                'log_probs': self.log_probs[:end_idx].view(-1),
                'dones': self.dones[:end_idx].view(-1),
            }
        else:
            # Sample random batch
            indices = torch.randint(0, self.size, (batch_size,), device=self.torch_device)
            env_indices = torch.randint(0, self.num_envs, (batch_size,), device=self.torch_device)
            
            batch_data = {
                'observations': self.observations[indices, env_indices],
                'actions': self.actions[indices, env_indices],
                'rewards': self.rewards[indices, env_indices],
                'values': self.values[indices, env_indices],
                'log_probs': self.log_probs[indices, env_indices],
                'dones': self.dones[indices, env_indices],
            }
        
        return batch_data
    
    def get_all_data(self) -> Dict[str, torch.Tensor]:
        """Get all data in the buffer."""
        return self.get_batch(batch_size=None)
    
    def is_full(self) -> bool:
        """Check if buffer is full."""
        return self.size >= self.buffer_size
    
    def is_empty(self) -> bool:
        """Check if buffer is empty."""
        return self.size == 0
    
    def __len__(self) -> int:
        """Get current buffer size."""
        return self.size
    
    def get_statistics(self) -> Dict[str, float]:
        """Get buffer statistics."""
        if self.size == 0:
            return {}
        
        with torch.no_grad():
            end_idx = self.size
            stats = {
                'buffer_size': self.size,
                'buffer_utilization': self.size / self.buffer_size,
                'mean_reward': self.rewards[:end_idx].mean().item(),
                'std_reward': self.rewards[:end_idx].std().item(),
                'mean_value': self.values[:end_idx].mean().item(),
                'std_value': self.values[:end_idx].std().item(),
                'done_rate': self.dones[:end_idx].float().mean().item(),
            }
        
        return stats


class RolloutBuffer(ExperienceBuffer):
    """
    Specialized buffer for on-policy algorithms like PPO.
    Collects full rollouts and provides them in sequence.
    """
    
    def __init__(self, 
                 rollout_length: int,
                 observation_size: int,
                 action_size: int,
                 num_envs: int = 1,
                 **kwargs):
        # For rollout buffer, buffer_size is the rollout length
        super().__init__(
            buffer_size=rollout_length,
            observation_size=observation_size,
            action_size=action_size,
            num_envs=num_envs,
            **kwargs
        )
        self.rollout_length = rollout_length
    
    def is_ready(self) -> bool:
        """Check if rollout is complete and ready for training."""
        return self.size >= self.rollout_length
    
    def get_rollout(self) -> Dict[str, torch.Tensor]:
        """Get the complete rollout for training."""
        if not self.is_ready():
            raise ValueError(f"Rollout not ready. Current size: {self.size}, Required: {self.rollout_length}")
        
        # Return data in sequence (time_steps, num_envs, feature_size)
        rollout_data = {
            'observations': self.observations[:self.rollout_length],
            'actions': self.actions[:self.rollout_length],
            'rewards': self.rewards[:self.rollout_length],
            'values': self.values[:self.rollout_length],
            'log_probs': self.log_probs[:self.rollout_length],
            'dones': self.dones[:self.rollout_length],
        }
        
        # Flatten for training (batch_size, feature_size)
        for key, value in rollout_data.items():
            if key in ['observations', 'actions']:
                rollout_data[key] = value.view(-1, value.size(-1))
            else:
                rollout_data[key] = value.view(-1)
        
        return rollout_data
    
    def clear(self) -> None:
        """Clear the buffer after using the rollout."""
        self.reset()
