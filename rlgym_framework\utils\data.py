"""Data processing utilities for the RLGym Framework."""

import numpy as np
import torch
from typing import Union, Tuple, Optional


def normalize_observations(observations: Union[np.ndarray, torch.Tensor], 
                         mean: Optional[Union[np.ndarray, torch.Tensor]] = None,
                         std: Optional[Union[np.ndarray, torch.Tensor]] = None) -> Union[np.ndarray, torch.Tensor]:
    """
    Normalize observations using mean and standard deviation.
    
    Args:
        observations: Input observations
        mean: Mean for normalization (computed if None)
        std: Standard deviation for normalization (computed if None)
        
    Returns:
        Normalized observations
    """
    if mean is None:
        if isinstance(observations, torch.Tensor):
            mean = torch.mean(observations, dim=0, keepdim=True)
        else:
            mean = np.mean(observations, axis=0, keepdims=True)
    
    if std is None:
        if isinstance(observations, torch.Tensor):
            std = torch.std(observations, dim=0, keepdim=True)
        else:
            std = np.std(observations, axis=0, keepdims=True)
    
    # Avoid division by zero
    if isinstance(observations, torch.Tensor):
        std = torch.clamp(std, min=1e-8)
    else:
        std = np.clip(std, a_min=1e-8, a_max=None)
    
    return (observations - mean) / std


def denormalize_actions(actions: Union[np.ndarray, torch.Tensor],
                       action_min: float = -1.0,
                       action_max: float = 1.0) -> Union[np.ndarray, torch.Tensor]:
    """
    Denormalize actions from [-1, 1] to [action_min, action_max].
    
    Args:
        actions: Normalized actions in [-1, 1]
        action_min: Minimum action value
        action_max: Maximum action value
        
    Returns:
        Denormalized actions
    """
    # Scale from [-1, 1] to [action_min, action_max]
    return (actions + 1.0) * (action_max - action_min) / 2.0 + action_min


def clip_actions(actions: Union[np.ndarray, torch.Tensor],
                action_min: float = -1.0,
                action_max: float = 1.0) -> Union[np.ndarray, torch.Tensor]:
    """
    Clip actions to valid range.
    
    Args:
        actions: Input actions
        action_min: Minimum action value
        action_max: Maximum action value
        
    Returns:
        Clipped actions
    """
    if isinstance(actions, torch.Tensor):
        return torch.clamp(actions, action_min, action_max)
    else:
        return np.clip(actions, action_min, action_max)


def compute_returns(rewards: Union[np.ndarray, torch.Tensor],
                   values: Union[np.ndarray, torch.Tensor],
                   dones: Union[np.ndarray, torch.Tensor],
                   gamma: float = 0.99) -> Union[np.ndarray, torch.Tensor]:
    """
    Compute discounted returns.
    
    Args:
        rewards: Reward sequence
        values: Value estimates
        dones: Done flags
        gamma: Discount factor
        
    Returns:
        Computed returns
    """
    if isinstance(rewards, torch.Tensor):
        returns = torch.zeros_like(rewards)
        running_return = torch.zeros_like(values[-1])
    else:
        returns = np.zeros_like(rewards)
        running_return = np.zeros_like(values[-1])
    
    for t in reversed(range(len(rewards))):
        if isinstance(rewards, torch.Tensor):
            running_return = rewards[t] + gamma * running_return * (1 - dones[t])
        else:
            running_return = rewards[t] + gamma * running_return * (1 - dones[t])
        returns[t] = running_return
    
    return returns


def compute_gae(rewards: Union[np.ndarray, torch.Tensor],
               values: Union[np.ndarray, torch.Tensor],
               dones: Union[np.ndarray, torch.Tensor],
               gamma: float = 0.99,
               gae_lambda: float = 0.95) -> Tuple[Union[np.ndarray, torch.Tensor], Union[np.ndarray, torch.Tensor]]:
    """
    Compute Generalized Advantage Estimation (GAE).
    
    Args:
        rewards: Reward sequence
        values: Value estimates
        dones: Done flags
        gamma: Discount factor
        gae_lambda: GAE lambda parameter
        
    Returns:
        Tuple of (advantages, returns)
    """
    if isinstance(rewards, torch.Tensor):
        advantages = torch.zeros_like(rewards)
        next_values = torch.cat([values[1:], torch.zeros_like(values[-1:])], dim=0)
    else:
        advantages = np.zeros_like(rewards)
        next_values = np.concatenate([values[1:], np.zeros_like(values[-1:])], axis=0)
    
    # Compute TD errors
    deltas = rewards + gamma * next_values * (1 - dones) - values
    
    # Compute GAE
    if isinstance(rewards, torch.Tensor):
        advantage = torch.zeros_like(values[-1])
    else:
        advantage = np.zeros_like(values[-1])
    
    for t in reversed(range(len(rewards))):
        advantage = deltas[t] + gamma * gae_lambda * (1 - dones[t]) * advantage
        advantages[t] = advantage
    
    returns = advantages + values
    
    return advantages, returns


def standardize_advantages(advantages: Union[np.ndarray, torch.Tensor]) -> Union[np.ndarray, torch.Tensor]:
    """
    Standardize advantages to have zero mean and unit variance.
    
    Args:
        advantages: Input advantages
        
    Returns:
        Standardized advantages
    """
    if isinstance(advantages, torch.Tensor):
        mean = torch.mean(advantages)
        std = torch.std(advantages)
        std = torch.clamp(std, min=1e-8)
    else:
        mean = np.mean(advantages)
        std = np.std(advantages)
        std = np.clip(std, a_min=1e-8, a_max=None)
    
    return (advantages - mean) / std


def create_mini_batches(data: dict, batch_size: int, shuffle: bool = True) -> list:
    """
    Create mini-batches from data dictionary.
    
    Args:
        data: Dictionary of arrays/tensors
        batch_size: Size of each mini-batch
        shuffle: Whether to shuffle the data
        
    Returns:
        List of mini-batch dictionaries
    """
    # Get the length from the first item
    data_length = len(next(iter(data.values())))
    
    # Create indices
    if isinstance(next(iter(data.values())), torch.Tensor):
        indices = torch.randperm(data_length) if shuffle else torch.arange(data_length)
    else:
        indices = np.random.permutation(data_length) if shuffle else np.arange(data_length)
    
    mini_batches = []
    for start_idx in range(0, data_length, batch_size):
        end_idx = min(start_idx + batch_size, data_length)
        batch_indices = indices[start_idx:end_idx]
        
        mini_batch = {}
        for key, value in data.items():
            mini_batch[key] = value[batch_indices]
        
        mini_batches.append(mini_batch)
    
    return mini_batches


def moving_average(data: Union[np.ndarray, list], window_size: int) -> Union[np.ndarray, list]:
    """
    Compute moving average of data.
    
    Args:
        data: Input data
        window_size: Size of the moving window
        
    Returns:
        Moving average
    """
    if isinstance(data, list):
        data = np.array(data)
    
    if len(data) < window_size:
        return data
    
    # Use convolution for efficient moving average
    kernel = np.ones(window_size) / window_size
    return np.convolve(data, kernel, mode='valid')


def exponential_moving_average(data: Union[np.ndarray, list], alpha: float = 0.1) -> Union[np.ndarray, list]:
    """
    Compute exponential moving average of data.
    
    Args:
        data: Input data
        alpha: Smoothing factor (0 < alpha <= 1)
        
    Returns:
        Exponential moving average
    """
    if isinstance(data, list):
        data = np.array(data)
    
    ema = np.zeros_like(data)
    ema[0] = data[0]
    
    for i in range(1, len(data)):
        ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]
    
    return ema
