"""Logging utilities for the RLGym Framework."""

import os
import logging
import sys
from typing import Any, Dict, Optional, Union
from pathlib import Path


class Logger:
    """
    Comprehensive logger with support for console, file, TensorBoard, and Weights & Biases.
    """
    
    def __init__(self,
                 log_dir: str = "./logs",
                 use_wandb: bool = False,
                 use_tensorboard: bool = True,
                 project_name: str = "rlgym_framework",
                 experiment_name: Optional[str] = None,
                 log_level: str = "INFO"):
        
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.use_wandb = use_wandb
        self.use_tensorboard = use_tensorboard
        self.project_name = project_name
        self.experiment_name = experiment_name or "experiment"
        
        # Set up Python logging
        self._setup_python_logging(log_level)
        
        # Set up TensorBoard
        self.tb_writer = None
        if use_tensorboard:
            self._setup_tensorboard()
        
        # Set up Weights & Biases
        self.wandb_run = None
        if use_wandb:
            self._setup_wandb()
    
    def _setup_python_logging(self, log_level: str):
        """Set up Python logging to console and file."""
        # Create logger
        self.logger = logging.getLogger("rlgym_framework")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_format)
        self.logger.addHandler(console_handler)
        
        # File handler
        log_file = self.log_dir / f"{self.experiment_name}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_format)
        self.logger.addHandler(file_handler)
    
    def _setup_tensorboard(self):
        """Set up TensorBoard logging."""
        try:
            from torch.utils.tensorboard import SummaryWriter
            tb_dir = self.log_dir / "tensorboard" / self.experiment_name
            self.tb_writer = SummaryWriter(log_dir=str(tb_dir))
            self.info(f"TensorBoard logging enabled. Run: tensorboard --logdir {tb_dir}")
        except ImportError:
            self.warning("TensorBoard not available. Install with: pip install tensorboard")
            self.use_tensorboard = False
    
    def _setup_wandb(self):
        """Set up Weights & Biases logging."""
        try:
            import wandb
            self.wandb_run = wandb.init(
                project=self.project_name,
                name=self.experiment_name,
                dir=str(self.log_dir)
            )
            self.info("Weights & Biases logging enabled")
        except ImportError:
            self.warning("Weights & Biases not available. Install with: pip install wandb")
            self.use_wandb = False
    
    def log(self, data: Dict[str, Any], step: Optional[int] = None):
        """
        Log data to all configured backends.
        
        Args:
            data: Dictionary of metrics to log
            step: Training step number
        """
        # Log to TensorBoard
        if self.tb_writer and step is not None:
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    self.tb_writer.add_scalar(key, value, step)
        
        # Log to Weights & Biases
        if self.wandb_run:
            log_data = data.copy()
            if step is not None:
                log_data['step'] = step
            self.wandb_run.log(log_data)
    
    def info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message."""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log error message."""
        self.logger.error(message)
    
    def debug(self, message: str):
        """Log debug message."""
        self.logger.debug(message)
    
    def close(self):
        """Close all logging backends."""
        if self.tb_writer:
            self.tb_writer.close()
        
        if self.wandb_run:
            self.wandb_run.finish()


def setup_logging(log_dir: str = "./logs", level: str = "INFO"):
    """
    Set up basic logging configuration.
    
    Args:
        log_dir: Directory for log files
        level: Logging level
    """
    log_dir = Path(log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_dir / "rlgym_framework.log")
        ]
    )
