"""
Bot training presets - The ultimate cheat code for making different types of bots!

This module contains preset configurations for training bots with different playstyles:
- Nexto-style: Mechanical, fast-paced, aggressive
- Ground Specialist: Dominates ground play, dribbling, flicks
- Aerial Specialist: Air roll shots, ceiling shots, aerial control
- All-Around: Balanced in all areas
- And many more!
"""

from typing import Dict, Any, List
from ..config.config import Config


# Base configurations for different skill focuses
BASE_CONFIGS = {
    "fast_training": {
        "training": {
            "total_timesteps": 5_000_000,
            "rollout_length": 4096,
            "batch_size": 128,
            "learning_rate": 5e-4,
            "ppo_epochs": 15,
            "gamma": 0.995,
            "gae_lambda": 0.98,
        }
    },
    "quality_training": {
        "training": {
            "total_timesteps": 20_000_000,
            "rollout_length": 8192,
            "batch_size": 256,
            "learning_rate": 3e-4,
            "ppo_epochs": 20,
            "gamma": 0.999,
            "gae_lambda": 0.95,
        }
    },
    "mechanical_focus": {
        "training": {
            "entropy_coef": 0.02,  # Higher exploration for mechanics
            "clip_ratio": 0.15,    # Tighter clipping for precision
        },
        "agent": {
            "hidden_sizes": [512, 512, 256],  # Larger network for complex mechanics
            "activation": "swish",
        }
    },
    "positioning_focus": {
        "training": {
            "entropy_coef": 0.005,  # Lower exploration, more consistent positioning
            "value_loss_coef": 1.0,  # Higher value learning for positioning
        },
        "agent": {
            "hidden_sizes": [256, 256, 128],
            "activation": "relu",
        }
    }
}


def create_nexto_config() -> Config:
    """
    Create a Nexto-style bot configuration.
    
    Nexto characteristics:
    - Extremely mechanical and fast
    - Aggressive playstyle
    - Excellent at air dribbles and flip resets
    - High-speed gameplay
    """
    config_dict = {
        "training": {
            "total_timesteps": 15_000_000,
            "rollout_length": 6144,
            "batch_size": 192,
            "learning_rate": 4e-4,
            "ppo_epochs": 18,
            "clip_ratio": 0.12,
            "entropy_coef": 0.025,  # High exploration for mechanical plays
            "gamma": 0.998,
            "gae_lambda": 0.97,
            "max_grad_norm": 0.8,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [768, 512, 256],  # Large network for complex mechanics
            "activation": "swish",
            "shared_layers": 2,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 6,  # Faster for mechanical training
            "spawn_opponents": True,
            "team_size": 1,
            "gravity": 1,
            "boost_consumption": 1,
            # Custom reward weights for Nexto-style
            "reward_weights": {
                "goal": 10.0,
                "save": 5.0,
                "shot": 3.0,
                "aerial_goal": 15.0,
                "air_dribble": 8.0,
                "flip_reset": 12.0,
                "speed": 2.0,
                "ball_touch": 1.0,
                "demo": 3.0,
            }
        },
        "experiment": {
            "name": "nexto_style_bot",
            "description": "Nexto-inspired mechanical and aggressive bot",
            "tags": ["nexto", "mechanical", "aggressive", "aerial"],
        }
    }
    return Config(config_dict)


def create_ground_specialist_config() -> Config:
    """
    Create a ground play specialist configuration.
    
    Ground specialist characteristics:
    - Excellent dribbling and flicks
    - Strong 50/50s and ground challenges
    - Precise ground shots
    - Good boost management on ground
    """
    config_dict = {
        "training": {
            "total_timesteps": 12_000_000,
            "rollout_length": 4096,
            "batch_size": 128,
            "learning_rate": 3e-4,
            "ppo_epochs": 12,
            "clip_ratio": 0.18,
            "entropy_coef": 0.015,
            "gamma": 0.996,
            "gae_lambda": 0.95,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [512, 384, 256],
            "activation": "relu",
            "shared_layers": 1,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 8,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "goal": 10.0,
                "save": 5.0,
                "shot": 4.0,
                "dribble": 6.0,
                "flick": 8.0,
                "ground_touch": 2.0,
                "fifty_fifty_win": 4.0,
                "boost_pickup": 1.0,
                "ball_carry": 3.0,
            }
        },
        "experiment": {
            "name": "ground_specialist_bot",
            "description": "Ground play specialist with excellent dribbling",
            "tags": ["ground", "dribbling", "flicks", "fifty_fifty"],
        }
    }
    return Config(config_dict)


def create_aerial_specialist_config() -> Config:
    """
    Create an aerial specialist configuration.
    
    Aerial specialist characteristics:
    - Dominant in the air
    - Air roll shots and ceiling shots
    - Excellent aerial car control
    - Strong aerial challenges
    """
    config_dict = {
        "training": {
            "total_timesteps": 18_000_000,
            "rollout_length": 8192,
            "batch_size": 256,
            "learning_rate": 2.5e-4,
            "ppo_epochs": 20,
            "clip_ratio": 0.15,
            "entropy_coef": 0.03,  # High exploration for aerial mechanics
            "gamma": 0.999,
            "gae_lambda": 0.98,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [1024, 768, 512, 256],  # Very large for complex aerial control
            "activation": "swish",
            "shared_layers": 2,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 6,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "goal": 10.0,
                "aerial_goal": 20.0,
                "ceiling_shot": 15.0,
                "air_roll_shot": 12.0,
                "aerial_save": 8.0,
                "aerial_touch": 3.0,
                "air_time": 2.0,
                "flip_reset": 10.0,
                "wall_touch": 2.0,
            }
        },
        "experiment": {
            "name": "aerial_specialist_bot",
            "description": "Aerial specialist with advanced air control",
            "tags": ["aerial", "ceiling_shots", "air_roll", "mechanics"],
        }
    }
    return Config(config_dict)


def create_all_around_config() -> Config:
    """
    Create an all-around balanced bot configuration.
    
    All-around characteristics:
    - Balanced in all areas
    - Good at everything, master of none
    - Adaptable playstyle
    - Consistent performance
    """
    config_dict = {
        "training": {
            "total_timesteps": 25_000_000,  # Longer training for balanced skills
            "rollout_length": 6144,
            "batch_size": 192,
            "learning_rate": 3e-4,
            "ppo_epochs": 15,
            "clip_ratio": 0.2,
            "entropy_coef": 0.01,
            "gamma": 0.998,
            "gae_lambda": 0.95,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [512, 512, 384, 256],
            "activation": "relu",
            "shared_layers": 2,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 8,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "goal": 10.0,
                "save": 6.0,
                "shot": 4.0,
                "aerial_goal": 12.0,
                "dribble": 4.0,
                "demo": 3.0,
                "boost_pickup": 1.0,
                "positioning": 2.0,
                "ball_touch": 1.5,
                "speed": 1.0,
            }
        },
        "experiment": {
            "name": "all_around_bot",
            "description": "Balanced bot good at everything",
            "tags": ["balanced", "all_around", "versatile"],
        }
    }
    return Config(config_dict)


def create_mechanical_config() -> Config:
    """
    Create a mechanical specialist configuration.

    Mechanical specialist characteristics:
    - Focus on advanced mechanics
    - Flip resets, air dribbles, ceiling shots
    - High-risk, high-reward plays
    - Creative and unpredictable
    """
    config_dict = {
        "training": {
            "total_timesteps": 20_000_000,
            "rollout_length": 8192,
            "batch_size": 256,
            "learning_rate": 2e-4,
            "ppo_epochs": 25,
            "clip_ratio": 0.1,  # Very tight clipping for precision
            "entropy_coef": 0.04,  # Very high exploration
            "gamma": 0.999,
            "gae_lambda": 0.98,
            "max_grad_norm": 1.0,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [1024, 768, 512, 384, 256],  # Very deep for complex mechanics
            "activation": "swish",
            "shared_layers": 3,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 4,  # Very fast for mechanical precision
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "flip_reset": 20.0,
                "air_dribble": 15.0,
                "ceiling_shot": 18.0,
                "wall_shot": 12.0,
                "double_tap": 16.0,
                "freestyle_goal": 25.0,
                "mechanical_touch": 5.0,
                "goal": 10.0,
                "creativity_bonus": 8.0,
            }
        },
        "experiment": {
            "name": "mechanical_specialist_bot",
            "description": "Advanced mechanical specialist bot",
            "tags": ["mechanical", "freestyle", "advanced", "creative"],
        }
    }
    return Config(config_dict)


def create_positioning_config() -> Config:
    """
    Create a positioning specialist configuration.

    Positioning specialist characteristics:
    - Excellent game sense and positioning
    - Smart rotations and defensive play
    - Efficient boost usage
    - Team-oriented play
    """
    config_dict = {
        "training": {
            "total_timesteps": 15_000_000,
            "rollout_length": 4096,
            "batch_size": 128,
            "learning_rate": 3e-4,
            "ppo_epochs": 10,
            "clip_ratio": 0.25,
            "entropy_coef": 0.005,  # Low exploration for consistent positioning
            "value_loss_coef": 1.5,  # High value learning
            "gamma": 0.999,
            "gae_lambda": 0.95,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [384, 256, 128],  # Smaller network, focus on decision making
            "activation": "relu",
            "shared_layers": 1,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 10,  # Slower for strategic thinking
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "positioning": 8.0,
                "rotation": 6.0,
                "defensive_play": 5.0,
                "boost_efficiency": 4.0,
                "save": 8.0,
                "clear": 4.0,
                "pass": 3.0,
                "goal": 10.0,
                "team_play": 5.0,
            }
        },
        "experiment": {
            "name": "positioning_specialist_bot",
            "description": "Smart positioning and game sense bot",
            "tags": ["positioning", "game_sense", "defensive", "smart"],
        }
    }
    return Config(config_dict)


def create_speed_demon_config() -> Config:
    """
    Create a speed demon configuration.

    Speed demon characteristics:
    - Extremely fast gameplay
    - Quick decisions and reactions
    - Speed-based plays and challenges
    - High-tempo pressure
    """
    config_dict = {
        "training": {
            "total_timesteps": 10_000_000,
            "rollout_length": 2048,  # Shorter rollouts for fast decisions
            "batch_size": 64,
            "learning_rate": 5e-4,
            "ppo_epochs": 8,
            "clip_ratio": 0.3,
            "entropy_coef": 0.02,
            "gamma": 0.99,  # Lower gamma for immediate rewards
            "gae_lambda": 0.9,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [256, 256],  # Smaller network for faster inference
            "activation": "relu",
            "shared_layers": 1,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 120,  # Faster game speed
            "tick_skip": 4,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "speed": 5.0,
                "quick_shot": 8.0,
                "fast_goal": 12.0,
                "speed_challenge": 6.0,
                "reaction_time": 4.0,
                "pressure": 3.0,
                "goal": 10.0,
                "demo": 5.0,
            }
        },
        "experiment": {
            "name": "speed_demon_bot",
            "description": "Ultra-fast speed demon bot",
            "tags": ["speed", "fast", "pressure", "tempo"],
        }
    }
    return Config(config_dict)


def create_wall_play_config() -> Config:
    """
    Create a wall play specialist configuration.

    Wall play specialist characteristics:
    - Excellent wall control and wall shots
    - Wall-to-air transitions
    - Wall dribbles and wall passes
    - Creative wall plays
    """
    config_dict = {
        "training": {
            "total_timesteps": 16_000_000,
            "rollout_length": 6144,
            "batch_size": 192,
            "learning_rate": 3e-4,
            "ppo_epochs": 15,
            "clip_ratio": 0.18,
            "entropy_coef": 0.02,
            "gamma": 0.998,
            "gae_lambda": 0.96,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [640, 512, 384, 256],
            "activation": "swish",
            "shared_layers": 2,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 6,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "wall_shot": 12.0,
                "wall_touch": 4.0,
                "wall_dribble": 8.0,
                "wall_pass": 6.0,
                "wall_to_air": 10.0,
                "wall_clear": 5.0,
                "goal": 10.0,
                "creative_wall_play": 7.0,
            }
        },
        "experiment": {
            "name": "wall_play_specialist_bot",
            "description": "Wall play specialist with creative wall control",
            "tags": ["wall_play", "wall_shots", "creative", "control"],
        }
    }
    return Config(config_dict)


def create_demo_hunter_config() -> Config:
    """
    Create a demo hunter configuration.

    Demo hunter characteristics:
    - Aggressive demolition play
    - Disruptive and annoying
    - Good at bumps and demos
    - Psychological warfare
    """
    config_dict = {
        "training": {
            "total_timesteps": 8_000_000,
            "rollout_length": 3072,
            "batch_size": 96,
            "learning_rate": 4e-4,
            "ppo_epochs": 12,
            "clip_ratio": 0.25,
            "entropy_coef": 0.03,
            "gamma": 0.995,
            "gae_lambda": 0.92,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [384, 256, 128],
            "activation": "relu",
            "shared_layers": 1,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 110,
            "tick_skip": 6,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "demo": 15.0,
                "bump": 8.0,
                "demo_goal": 20.0,
                "disruption": 5.0,
                "aggressive_play": 6.0,
                "goal": 10.0,
                "save": 4.0,
                "speed": 3.0,
            }
        },
        "experiment": {
            "name": "demo_hunter_bot",
            "description": "Aggressive demo hunter bot",
            "tags": ["demo", "aggressive", "disruptive", "bumps"],
        }
    }
    return Config(config_dict)


def create_defensive_config() -> Config:
    """
    Create a defensive specialist configuration.

    Defensive specialist characteristics:
    - Excellent saves and clears
    - Strong defensive positioning
    - Good at reading opponent plays
    - Consistent and reliable
    """
    config_dict = {
        "training": {
            "total_timesteps": 12_000_000,
            "rollout_length": 4096,
            "batch_size": 128,
            "learning_rate": 2.5e-4,
            "ppo_epochs": 12,
            "clip_ratio": 0.22,
            "entropy_coef": 0.008,
            "value_loss_coef": 1.2,
            "gamma": 0.999,
            "gae_lambda": 0.95,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [512, 384, 256],
            "activation": "relu",
            "shared_layers": 1,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 8,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_weights": {
                "save": 15.0,
                "clear": 8.0,
                "defensive_positioning": 6.0,
                "block_shot": 10.0,
                "goal_prevention": 12.0,
                "defensive_touch": 4.0,
                "goal": 10.0,
                "consistency": 5.0,
            }
        },
        "experiment": {
            "name": "defensive_specialist_bot",
            "description": "Defensive specialist with excellent saves",
            "tags": ["defensive", "saves", "positioning", "reliable"],
        }
    }
    return Config(config_dict)


# Updated Preset registry with all bot styles
PRESET_REGISTRY = {
    # Pro player inspired styles
    "nexto": create_nexto_config,
    "opti": create_opti_config,
    "ripple": create_ripple_config,
    "seer": create_seer_config,

    # Specialized styles
    "ground": create_ground_specialist_config,
    "aerial": create_aerial_specialist_config,
    "all_around": create_all_around_config,
    "mechanical": create_mechanical_config,
    "freestyle": create_freestyle_config,
    "positioning": create_positioning_config,
    "speed": create_speed_demon_config,
    "wall_play": create_wall_play_config,
    "demo": create_demo_hunter_config,
    "defensive": create_defensive_config,
}


def get_preset_config(preset_name: str) -> Config:
    """
    Get a preset configuration by name.

    Args:
        preset_name: Name of the preset

    Returns:
        Config instance for the preset

    Raises:
        ValueError: If preset name is not found
    """
    if preset_name.lower() not in PRESET_REGISTRY:
        available = ", ".join(PRESET_REGISTRY.keys())
        raise ValueError(f"Unknown preset '{preset_name}'. Available presets: {available}")

    return PRESET_REGISTRY[preset_name.lower()]()


def list_available_presets() -> List[str]:
    """
    List all available preset names.

    Returns:
        List of preset names
    """
    return list(PRESET_REGISTRY.keys())


def get_preset_descriptions() -> Dict[str, str]:
    """
    Get descriptions of all available presets.

    Returns:
        Dictionary mapping preset names to descriptions
    """
    descriptions = {}
    for name, create_func in PRESET_REGISTRY.items():
        config = create_func()
        descriptions[name] = config.experiment.get("description", "No description available")

    return descriptions


# Add new bot styles
def create_opti_config() -> Config:
    """
    Create an Opti-style bot configuration.

    Opti characteristics:
    - Master of positioning and game sense
    - Excellent boost management
    - Smart rotations and efficient plays
    - Consistent and reliable
    """
    config_dict = {
        "training": {
            "total_timesteps": 20_000_000,
            "rollout_length": 4096,
            "batch_size": 128,
            "learning_rate": 2.5e-4,
            "ppo_epochs": 12,
            "clip_ratio": 0.22,
            "entropy_coef": 0.008,  # Low exploration for consistency
            "value_loss_coef": 1.2,  # High value learning for positioning
            "gamma": 0.999,
            "gae_lambda": 0.95,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [384, 256, 128],  # Smaller network for efficiency
            "activation": "relu",
            "shared_layers": 1,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 8,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_style": "opti",  # Uses OptiRewards
        },
        "experiment": {
            "name": "opti_style_bot",
            "description": "Opti-inspired positioning master with excellent game sense",
            "tags": ["opti", "positioning", "game_sense", "efficient"],
        }
    }
    return Config(config_dict)


def create_ripple_config() -> Config:
    """
    Create a Ripple-style bot configuration.

    Ripple characteristics:
    - Mechanical genius with creative plays
    - Ceiling shots and wall plays
    - Unpredictable and innovative
    - Advanced mechanics focus
    """
    config_dict = {
        "training": {
            "total_timesteps": 25_000_000,
            "rollout_length": 8192,
            "batch_size": 256,
            "learning_rate": 2e-4,
            "ppo_epochs": 25,
            "clip_ratio": 0.1,  # Very tight for precision
            "entropy_coef": 0.04,  # High exploration for creativity
            "gamma": 0.999,
            "gae_lambda": 0.98,
            "max_grad_norm": 1.0,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [1024, 768, 512, 384, 256],  # Very deep for mechanics
            "activation": "swish",
            "shared_layers": 3,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 4,  # Fast for mechanical precision
            "spawn_opponents": True,
            "team_size": 1,
            "reward_style": "ripple",  # Uses RippleRewards
        },
        "experiment": {
            "name": "ripple_style_bot",
            "description": "Ripple-inspired mechanical genius with creative plays",
            "tags": ["ripple", "mechanical", "creative", "ceiling_shots"],
        }
    }
    return Config(config_dict)


def create_seer_config() -> Config:
    """
    Create a Seer-style bot configuration.

    Seer characteristics:
    - Defensive wall and incredible saves
    - Excellent positioning and reads
    - Consistent and reliable defense
    - Master of clears and blocks
    """
    config_dict = {
        "training": {
            "total_timesteps": 18_000_000,
            "rollout_length": 4096,
            "batch_size": 128,
            "learning_rate": 2.5e-4,
            "ppo_epochs": 15,
            "clip_ratio": 0.2,
            "entropy_coef": 0.006,  # Low exploration for consistency
            "value_loss_coef": 1.5,  # High value learning for defense
            "gamma": 0.999,
            "gae_lambda": 0.95,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [512, 384, 256],
            "activation": "relu",
            "shared_layers": 1,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 8,
            "spawn_opponents": True,
            "team_size": 1,
            "reward_style": "seer",  # Uses SeerRewards
        },
        "experiment": {
            "name": "seer_style_bot",
            "description": "Seer-inspired defensive specialist with incredible saves",
            "tags": ["seer", "defensive", "saves", "positioning"],
        }
    }
    return Config(config_dict)


def create_freestyle_config() -> Config:
    """
    Create a freestyle specialist configuration.

    Freestyle characteristics:
    - All about style points and flair
    - Air dribbles, flip resets, ceiling shots
    - Creative and unpredictable goals
    - Mechanical showoff
    """
    config_dict = {
        "training": {
            "total_timesteps": 30_000_000,  # Longer training for style
            "rollout_length": 8192,
            "batch_size": 256,
            "learning_rate": 1.5e-4,
            "ppo_epochs": 30,
            "clip_ratio": 0.08,  # Very tight for precision
            "entropy_coef": 0.05,  # Very high exploration for creativity
            "gamma": 0.999,
            "gae_lambda": 0.98,
            "max_grad_norm": 1.2,
        },
        "agent": {
            "algorithm": "PPO",
            "hidden_sizes": [1024, 768, 512, 384, 256, 128],  # Very deep
            "activation": "swish",
            "shared_layers": 4,
        },
        "environment": {
            "environment": "RLGym",
            "game_speed": 100,
            "tick_skip": 4,  # Fast for mechanical precision
            "spawn_opponents": True,
            "team_size": 1,
            "reward_style": "freestyle",  # Uses FreestyleRewards
        },
        "experiment": {
            "name": "freestyle_specialist_bot",
            "description": "Freestyle specialist focused on style and mechanical flair",
            "tags": ["freestyle", "style", "air_dribbles", "flip_resets"],
        }
    }
    return Config(config_dict)
