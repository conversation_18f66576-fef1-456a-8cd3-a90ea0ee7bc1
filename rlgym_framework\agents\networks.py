"""Neural network architectures for RL agents."""

import math
from typing import List, Optional, Tuple, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
from ..core.base import BaseComponent


class MLPNetwork(nn.Module, BaseComponent):
    """
    Multi-Layer Perceptron network with configurable architecture.
    Optimized for performance across different compute backends.
    """
    
    def __init__(self, 
                 input_size: int,
                 output_size: int,
                 hidden_sizes: List[int] = [256, 256],
                 activation: str = "relu",
                 dropout: float = 0.0,
                 batch_norm: bool = False,
                 layer_norm: bool = False,
                 **kwargs):
        nn.Module.__init__(self)
        BaseComponent.__init__(self, **kwargs)
        
        self.input_size = input_size
        self.output_size = output_size
        self.hidden_sizes = hidden_sizes
        self.activation = activation
        self.dropout = dropout
        self.batch_norm = batch_norm
        self.layer_norm = layer_norm
        
        # Build network layers
        self.layers = nn.ModuleList()
        self.norms = nn.ModuleList() if (batch_norm or layer_norm) else None
        self.dropouts = nn.ModuleList() if dropout > 0 else None
        
        # Input layer
        prev_size = input_size
        for hidden_size in hidden_sizes:
            self.layers.append(nn.Linear(prev_size, hidden_size))
            
            # Add normalization if specified
            if batch_norm:
                self.norms.append(nn.BatchNorm1d(hidden_size))
            elif layer_norm:
                self.norms.append(nn.LayerNorm(hidden_size))
            
            # Add dropout if specified
            if dropout > 0:
                self.dropouts.append(nn.Dropout(dropout))
            
            prev_size = hidden_size
        
        # Output layer
        self.output_layer = nn.Linear(prev_size, output_size)
        
        # Initialize weights
        self._initialize_weights()
        
        # Move to device
        self.to(self.torch_device)
    
    def _initialize_weights(self):
        """Initialize network weights using Xavier/He initialization."""
        for layer in self.layers:
            if self.activation.lower() in ['relu', 'leaky_relu', 'elu']:
                # He initialization for ReLU-like activations
                nn.init.kaiming_normal_(layer.weight, nonlinearity=self.activation.lower())
            else:
                # Xavier initialization for other activations
                nn.init.xavier_normal_(layer.weight)
            nn.init.zeros_(layer.bias)
        
        # Initialize output layer with smaller weights
        nn.init.xavier_normal_(self.output_layer.weight, gain=0.01)
        nn.init.zeros_(self.output_layer.bias)
    
    def _get_activation(self):
        """Get activation function."""
        activations = {
            'relu': F.relu,
            'leaky_relu': F.leaky_relu,
            'elu': F.elu,
            'tanh': torch.tanh,
            'sigmoid': torch.sigmoid,
            'swish': lambda x: x * torch.sigmoid(x),
            'gelu': F.gelu,
        }
        return activations.get(self.activation.lower(), F.relu)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network."""
        activation_fn = self._get_activation()
        
        for i, layer in enumerate(self.layers):
            x = layer(x)
            
            # Apply normalization
            if self.norms is not None:
                x = self.norms[i](x)
            
            # Apply activation
            x = activation_fn(x)
            
            # Apply dropout
            if self.dropouts is not None:
                x = self.dropouts[i](x)
        
        # Output layer (no activation)
        x = self.output_layer(x)
        return x
    
    def reset(self):
        """Reset network parameters."""
        self._initialize_weights()


class ActorCriticNetwork(nn.Module, BaseComponent):
    """
    Actor-Critic network architecture for policy gradient methods.
    Supports shared or separate networks for actor and critic.
    """
    
    def __init__(self,
                 observation_size: int,
                 action_size: int,
                 hidden_sizes: List[int] = [256, 256],
                 shared_layers: int = 1,
                 activation: str = "relu",
                 action_std_init: float = 0.6,
                 **kwargs):
        nn.Module.__init__(self)
        BaseComponent.__init__(self, **kwargs)
        
        self.observation_size = observation_size
        self.action_size = action_size
        self.hidden_sizes = hidden_sizes
        self.shared_layers = shared_layers
        self.activation = activation
        self.action_std_init = action_std_init
        
        # Shared layers
        if shared_layers > 0:
            shared_hidden = hidden_sizes[:shared_layers]
            self.shared_net = MLPNetwork(
                observation_size, 
                hidden_sizes[shared_layers-1],
                shared_hidden[:-1],
                activation=activation,
                **kwargs
            )
            remaining_input_size = hidden_sizes[shared_layers-1]
            remaining_hidden = hidden_sizes[shared_layers:]
        else:
            self.shared_net = None
            remaining_input_size = observation_size
            remaining_hidden = hidden_sizes
        
        # Actor network (policy)
        if remaining_hidden:
            self.actor_net = MLPNetwork(
                remaining_input_size,
                remaining_hidden[-1],
                remaining_hidden[:-1],
                activation=activation,
                **kwargs
            )
            actor_input_size = remaining_hidden[-1]
        else:
            self.actor_net = None
            actor_input_size = remaining_input_size
        
        # Actor output layers
        self.action_mean = nn.Linear(actor_input_size, action_size)
        self.action_log_std = nn.Parameter(
            torch.ones(action_size) * math.log(action_std_init)
        )
        
        # Critic network (value function)
        if remaining_hidden:
            self.critic_net = MLPNetwork(
                remaining_input_size,
                remaining_hidden[-1],
                remaining_hidden[:-1],
                activation=activation,
                **kwargs
            )
            critic_input_size = remaining_hidden[-1]
        else:
            self.critic_net = None
            critic_input_size = remaining_input_size
        
        self.value_head = nn.Linear(critic_input_size, 1)
        
        # Initialize weights
        self._initialize_weights()
        
        # Move to device
        self.to(self.torch_device)
    
    def _initialize_weights(self):
        """Initialize network weights."""
        # Initialize action mean with small weights
        nn.init.xavier_normal_(self.action_mean.weight, gain=0.01)
        nn.init.zeros_(self.action_mean.bias)
        
        # Initialize value head
        nn.init.xavier_normal_(self.value_head.weight)
        nn.init.zeros_(self.value_head.bias)
    
    def forward(self, observations: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass returning action distribution and value.
        
        Returns:
            Tuple of (action_mean, value)
        """
        # Shared features
        if self.shared_net is not None:
            shared_features = self.shared_net(observations)
        else:
            shared_features = observations
        
        # Actor forward pass
        if self.actor_net is not None:
            actor_features = self.actor_net(shared_features)
        else:
            actor_features = shared_features
        
        action_mean = self.action_mean(actor_features)
        
        # Critic forward pass
        if self.critic_net is not None:
            critic_features = self.critic_net(shared_features)
        else:
            critic_features = shared_features
        
        value = self.value_head(critic_features)
        
        return action_mean, value
    
    def get_action_distribution(self, observations: torch.Tensor) -> torch.distributions.Normal:
        """Get action distribution for sampling."""
        action_mean, _ = self.forward(observations)
        action_std = torch.exp(self.action_log_std)
        return torch.distributions.Normal(action_mean, action_std)
    
    def get_value(self, observations: torch.Tensor) -> torch.Tensor:
        """Get value estimate."""
        _, value = self.forward(observations)
        return value
    
    def evaluate_actions(self, observations: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Evaluate actions for training.
        
        Returns:
            Tuple of (log_probs, values, entropy)
        """
        action_mean, value = self.forward(observations)
        action_std = torch.exp(self.action_log_std)
        
        dist = torch.distributions.Normal(action_mean, action_std)
        log_probs = dist.log_prob(actions).sum(dim=-1, keepdim=True)
        entropy = dist.entropy().sum(dim=-1, keepdim=True)
        
        return log_probs, value, entropy
    
    def reset(self):
        """Reset network parameters."""
        if self.shared_net is not None:
            self.shared_net.reset()
        if self.actor_net is not None:
            self.actor_net.reset()
        if self.critic_net is not None:
            self.critic_net.reset()
        self._initialize_weights()
